/* AR Image Tracking Styles */
* {
    box-sizing: border-box;
}

body {
    margin: 0;
    padding: 0;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: #000;
    overflow: hidden;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    -webkit-touch-callout: none;
    -webkit-tap-highlight-color: transparent;
}

/* Loading Screen */
#loading {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    z-index: 1000;
    transition: opacity 0.5s ease-out;
}

#loading.hidden {
    opacity: 0;
    pointer-events: none;
}

.spinner {
    border: 4px solid rgba(255, 255, 255, 0.3);
    border-top: 4px solid #ffffff;
    border-radius: 50%;
    width: 60px;
    height: 60px;
    animation: spin 1s linear infinite;
    margin-bottom: 30px;
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }

    100% {
        transform: rotate(360deg);
    }
}

#loading p {
    font-size: 18px;
    margin: 10px 0;
    text-align: center;
    opacity: 0.9;
}

/* Instructions Panel */
#instructions {
    position: fixed;
    top: env(safe-area-inset-top, 10px);
    left: env(safe-area-inset-left, 10px);
    right: env(safe-area-inset-right, 10px);
    background: rgba(0, 0, 0, 0.9);
    color: white;
    padding: 15px;
    border-radius: 12px;
    z-index: 100;
    font-size: clamp(12px, 2.5vw, 14px);
    backdrop-filter: blur(15px);
    border: 1px solid rgba(255, 255, 255, 0.15);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.4);
    transition: all 0.3s ease;
    max-height: 40vh;
    overflow-y: auto;
}

#instructions h3 {
    margin: 0 0 12px 0;
    color: #4ECDC4;
    font-size: clamp(14px, 3vw, 18px);
    font-weight: 600;
}

#instructions p {
    margin: 6px 0;
    line-height: 1.5;
    opacity: 0.95;
    font-size: clamp(11px, 2.2vw, 13px);
}

#instructions ol,
#instructions ul {
    padding-left: 18px;
    margin: 8px 0;
}

#instructions li {
    margin: 4px 0;
    line-height: 1.4;
}

/* Toggle Button */
#toggleList {
    position: fixed;
    bottom: calc(25vh + env(safe-area-inset-bottom, 20px));
    right: env(safe-area-inset-right, 15px);
    background: linear-gradient(135deg, #4ECDC4, #44A08D);
    color: white;
    border: none;
    padding: 0;
    border-radius: 50%;
    cursor: pointer;
    z-index: 101;
    font-size: clamp(16px, 4vw, 20px);
    width: clamp(50px, 12vw, 60px);
    height: clamp(50px, 12vw, 60px);
    box-shadow: 0 4px 20px rgba(78, 205, 196, 0.4);
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    touch-action: manipulation;
}

#toggleList:hover {
    transform: scale(1.1);
    box-shadow: 0 6px 25px rgba(78, 205, 196, 0.6);
}

#toggleList:active {
    transform: scale(0.95);
}

/* Image List Panel */
#imageList {
    position: fixed;
    bottom: env(safe-area-inset-bottom, 10px);
    left: env(safe-area-inset-left, 10px);
    right: env(safe-area-inset-right, 10px);
    background: rgba(0, 0, 0, 0.9);
    color: white;
    padding: 15px;
    border-radius: 12px;
    z-index: 100;
    max-height: 25vh;
    overflow-y: auto;
    backdrop-filter: blur(15px);
    border: 1px solid rgba(255, 255, 255, 0.15);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.4);
    transition: all 0.3s ease;
    -webkit-overflow-scrolling: touch;
}

#imageList h4 {
    margin: 0 0 12px 0;
    color: #4ECDC4;
    font-size: clamp(14px, 3vw, 16px);
    font-weight: 600;
}

#imageContainer {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

/* Image Items */
.image-item {
    display: flex;
    align-items: center;
    padding: clamp(8px, 2vw, 12px);
    background: rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    border: 1px solid transparent;
    touch-action: manipulation;
    min-height: 60px;
}

.image-item:hover {
    background: rgba(255, 255, 255, 0.2);
    border-color: #4ECDC4;
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(78, 205, 196, 0.3);
}

.image-item:active {
    transform: translateY(0);
}

.image-item img {
    width: clamp(50px, 12vw, 70px);
    height: clamp(35px, 9vw, 50px);
    object-fit: cover;
    border-radius: 6px;
    margin-right: clamp(10px, 3vw, 15px);
    border: 2px solid rgba(255, 255, 255, 0.2);
    transition: all 0.3s ease;
    flex-shrink: 0;
}

.image-item:hover img,
.image-item:active img {
    border-color: #4ECDC4;
    transform: scale(1.05);
}

.image-info {
    flex: 1;
    font-size: clamp(11px, 2.5vw, 13px);
    min-width: 0;
    overflow: hidden;
}

.image-author {
    font-weight: 600;
    margin-bottom: 3px;
    color: #ffffff;
    font-size: clamp(12px, 2.8vw, 14px);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.image-id {
    opacity: 0.75;
    font-size: clamp(10px, 2.2vw, 12px);
    color: #cccccc;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* Scrollbar Styling */
#imageList::-webkit-scrollbar {
    width: 6px;
}

#imageList::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 3px;
}

#imageList::-webkit-scrollbar-thumb {
    background: #4ECDC4;
    border-radius: 3px;
}

#imageList::-webkit-scrollbar-thumb:hover {
    background: #44A08D;
}

/* Notification Animations */
@keyframes fadeInOut {
    0% {
        opacity: 0;
        transform: translate(-50%, -50%) scale(0.8);
    }

    20% {
        opacity: 1;
        transform: translate(-50%, -50%) scale(1.05);
    }

    25% {
        transform: translate(-50%, -50%) scale(1);
    }

    80% {
        opacity: 1;
        transform: translate(-50%, -50%) scale(1);
    }

    100% {
        opacity: 0;
        transform: translate(-50%, -50%) scale(0.8);
    }
}

/* Responsive Design */

/* Large screens (Desktop) */
@media (min-width: 1200px) {
    #instructions {
        max-width: 500px;
        left: 50%;
        transform: translateX(-50%);
        right: auto;
    }

    #imageList {
        max-width: 800px;
        left: 50%;
        transform: translateX(-50%);
        right: auto;
        max-height: 30vh;
    }

    #imageContainer {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 12px;
    }
}

/* Tablet Portrait */
@media (max-width: 768px) and (orientation: portrait) {
    #instructions {
        max-height: 35vh;
        font-size: 13px;
    }

    #imageList {
        max-height: 30vh;
    }

    #toggleList {
        bottom: calc(32vh + env(safe-area-inset-bottom, 15px));
    }
}

/* Tablet Landscape */
@media (max-width: 1024px) and (orientation: landscape) {
    #instructions {
        max-height: 45vh;
        left: env(safe-area-inset-left, 20px);
        right: 40%;
    }

    #imageList {
        max-height: 40vh;
        left: env(safe-area-inset-left, 20px);
        right: 40%;
    }

    #toggleList {
        right: calc(38% + env(safe-area-inset-right, 15px));
    }
}

/* Mobile Portrait */
@media (max-width: 480px) and (orientation: portrait) {
    #instructions {
        top: env(safe-area-inset-top, 5px);
        left: env(safe-area-inset-left, 8px);
        right: env(safe-area-inset-right, 8px);
        padding: 12px;
        max-height: 40vh;
    }

    #imageList {
        bottom: env(safe-area-inset-bottom, 5px);
        left: env(safe-area-inset-left, 8px);
        right: env(safe-area-inset-right, 8px);
        padding: 12px;
        max-height: 25vh;
    }

    #toggleList {
        bottom: calc(27vh + env(safe-area-inset-bottom, 10px));
        right: env(safe-area-inset-right, 12px);
    }
}

/* Mobile Landscape */
@media (max-width: 896px) and (orientation: landscape) {
    #instructions {
        top: env(safe-area-inset-top, 5px);
        left: env(safe-area-inset-left, 10px);
        right: 45%;
        max-height: 60vh;
        font-size: 12px;
    }

    #imageList {
        bottom: env(safe-area-inset-bottom, 5px);
        left: env(safe-area-inset-left, 10px);
        right: 45%;
        max-height: 35vh;
        padding: 10px;
    }

    #toggleList {
        right: calc(43% + env(safe-area-inset-right, 10px));
        bottom: calc(37vh + env(safe-area-inset-bottom, 10px));
    }

    .image-item {
        padding: 8px;
        min-height: 50px;
    }
}

/* Very small screens */
@media (max-width: 360px) {
    #instructions h3 {
        font-size: 14px;
    }

    #instructions p {
        font-size: 11px;
    }

    #imageList h4 {
        font-size: 13px;
    }

    .image-item {
        padding: 6px;
        min-height: 50px;
    }

    .image-item img {
        width: 45px;
        height: 32px;
    }
}

/* High DPI screens */
@media (-webkit-min-device-pixel-ratio: 2),
(min-resolution: 192dpi) {

    #instructions,
    #imageList {
        backdrop-filter: blur(20px);
    }

    .image-item img {
        image-rendering: -webkit-optimize-contrast;
        image-rendering: crisp-edges;
    }
}

/* A-Frame Scene Styling */
a-scene {
    width: 100vw;
    height: 100vh;
}

/* Error Styling */
.error-container {
    color: #ff6b6b;
    text-align: center;
    padding: 20px;
}

.error-container h3 {
    margin-bottom: 15px;
    font-size: 24px;
}

.error-container p {
    margin-bottom: 20px;
    font-size: 16px;
    line-height: 1.5;
}

.error-container button {
    padding: 12px 24px;
    background: linear-gradient(135deg, #4ECDC4, #44A08D);
    color: white;
    border: none;
    border-radius: 25px;
    cursor: pointer;
    font-size: 16px;
    font-weight: bold;
    transition: all 0.3s ease;
}

.error-container button:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(78, 205, 196, 0.4);
}