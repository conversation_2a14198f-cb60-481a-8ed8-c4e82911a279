/* AR Image Tracking Styles */
body {
    margin: 0;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: #000;
    overflow: hidden;
}

/* Loading Screen */
#loading {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    z-index: 1000;
    transition: opacity 0.5s ease-out;
}

#loading.hidden {
    opacity: 0;
    pointer-events: none;
}

.spinner {
    border: 4px solid rgba(255, 255, 255, 0.3);
    border-top: 4px solid #ffffff;
    border-radius: 50%;
    width: 60px;
    height: 60px;
    animation: spin 1s linear infinite;
    margin-bottom: 30px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

#loading p {
    font-size: 18px;
    margin: 10px 0;
    text-align: center;
    opacity: 0.9;
}

/* Instructions Panel */
#instructions {
    position: fixed;
    top: 20px;
    left: 20px;
    right: 20px;
    background: rgba(0, 0, 0, 0.85);
    color: white;
    padding: 20px;
    border-radius: 15px;
    z-index: 100;
    font-size: 14px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
    transition: all 0.3s ease;
}

#instructions h3 {
    margin: 0 0 15px 0;
    color: #4ECDC4;
    font-size: 18px;
}

#instructions p {
    margin: 8px 0;
    line-height: 1.4;
    opacity: 0.9;
}

/* Toggle Button */
#toggleList {
    position: fixed;
    bottom: 240px;
    right: 20px;
    background: linear-gradient(135deg, #4ECDC4, #44A08D);
    color: white;
    border: none;
    padding: 15px;
    border-radius: 50%;
    cursor: pointer;
    z-index: 101;
    font-size: 20px;
    width: 60px;
    height: 60px;
    box-shadow: 0 4px 20px rgba(78, 205, 196, 0.4);
    transition: all 0.3s ease;
}

#toggleList:hover {
    transform: scale(1.1);
    box-shadow: 0 6px 25px rgba(78, 205, 196, 0.6);
}

#toggleList:active {
    transform: scale(0.95);
}

/* Image List Panel */
#imageList {
    position: fixed;
    bottom: 20px;
    left: 20px;
    right: 20px;
    background: rgba(0, 0, 0, 0.85);
    color: white;
    padding: 20px;
    border-radius: 15px;
    z-index: 100;
    max-height: 200px;
    overflow-y: auto;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
    transition: all 0.3s ease;
}

#imageList h4 {
    margin: 0 0 15px 0;
    color: #4ECDC4;
    font-size: 16px;
}

#imageContainer {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

/* Image Items */
.image-item {
    display: flex;
    align-items: center;
    padding: 12px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 10px;
    cursor: pointer;
    transition: all 0.3s ease;
    border: 1px solid transparent;
}

.image-item:hover {
    background: rgba(255, 255, 255, 0.2);
    border-color: #4ECDC4;
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(78, 205, 196, 0.3);
}

.image-item:active {
    transform: translateY(0);
}

.image-item img {
    width: 70px;
    height: 50px;
    object-fit: cover;
    border-radius: 8px;
    margin-right: 15px;
    border: 2px solid rgba(255, 255, 255, 0.2);
    transition: all 0.3s ease;
}

.image-item:hover img {
    border-color: #4ECDC4;
    transform: scale(1.05);
}

.image-info {
    flex: 1;
    font-size: 13px;
}

.image-author {
    font-weight: bold;
    margin-bottom: 4px;
    color: #ffffff;
    font-size: 14px;
}

.image-id {
    opacity: 0.7;
    font-size: 12px;
    color: #cccccc;
}

/* Scrollbar Styling */
#imageList::-webkit-scrollbar {
    width: 6px;
}

#imageList::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 3px;
}

#imageList::-webkit-scrollbar-thumb {
    background: #4ECDC4;
    border-radius: 3px;
}

#imageList::-webkit-scrollbar-thumb:hover {
    background: #44A08D;
}

/* Notification Animations */
@keyframes fadeInOut {
    0% { 
        opacity: 0; 
        transform: translate(-50%, -50%) scale(0.8); 
    }
    20% { 
        opacity: 1; 
        transform: translate(-50%, -50%) scale(1.05); 
    }
    25% { 
        transform: translate(-50%, -50%) scale(1); 
    }
    80% { 
        opacity: 1; 
        transform: translate(-50%, -50%) scale(1); 
    }
    100% { 
        opacity: 0; 
        transform: translate(-50%, -50%) scale(0.8); 
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    #instructions {
        left: 10px;
        right: 10px;
        top: 10px;
        padding: 15px;
        font-size: 13px;
    }
    
    #instructions h3 {
        font-size: 16px;
    }
    
    #imageList {
        left: 10px;
        right: 10px;
        bottom: 10px;
        max-height: 150px;
        padding: 15px;
    }
    
    #toggleList {
        bottom: 180px;
        right: 15px;
        width: 50px;
        height: 50px;
        font-size: 18px;
    }
    
    .image-item {
        padding: 10px;
    }
    
    .image-item img {
        width: 60px;
        height: 40px;
        margin-right: 12px;
    }
    
    .image-info {
        font-size: 12px;
    }
    
    .image-author {
        font-size: 13px;
    }
}

@media (max-width: 480px) {
    #instructions {
        padding: 12px;
        font-size: 12px;
    }
    
    #instructions h3 {
        font-size: 15px;
        margin-bottom: 10px;
    }
    
    #instructions p {
        margin: 6px 0;
    }
    
    #imageList {
        padding: 12px;
        max-height: 120px;
    }
    
    #imageList h4 {
        font-size: 14px;
        margin-bottom: 10px;
    }
    
    .image-item {
        padding: 8px;
    }
    
    .image-item img {
        width: 50px;
        height: 35px;
        margin-right: 10px;
    }
}

/* A-Frame Scene Styling */
a-scene {
    width: 100vw;
    height: 100vh;
}

/* Error Styling */
.error-container {
    color: #ff6b6b;
    text-align: center;
    padding: 20px;
}

.error-container h3 {
    margin-bottom: 15px;
    font-size: 24px;
}

.error-container p {
    margin-bottom: 20px;
    font-size: 16px;
    line-height: 1.5;
}

.error-container button {
    padding: 12px 24px;
    background: linear-gradient(135deg, #4ECDC4, #44A08D);
    color: white;
    border: none;
    border-radius: 25px;
    cursor: pointer;
    font-size: 16px;
    font-weight: bold;
    transition: all 0.3s ease;
}

.error-container button:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(78, 205, 196, 0.4);
}
