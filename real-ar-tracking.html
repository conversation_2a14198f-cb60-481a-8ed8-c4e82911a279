<!DOCTYPE html>
<html lang="vi">

<head>
    <meta charset="UTF-8">
    <meta name="viewport"
        content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no, viewport-fit=cover">
    <title>Real AR Image Tracking</title>
    <script src="https://aframe.io/releases/1.4.0/aframe.min.js"></script>
    <script src="https://cdn.jsdelivr.net/gh/AR-js-org/AR.js@3.4.5/aframe/build/aframe-ar-nft.js"></script>
    <style>
        body {
            margin: 0;
            font-family: Arial, sans-serif;
            background: #000;
            overflow: hidden;
        }

        #loading {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            z-index: 1000;
        }

        #loading.hidden {
            display: none;
        }

        .spinner {
            border: 4px solid rgba(255, 255, 255, 0.3);
            border-top: 4px solid #ffffff;
            border-radius: 50%;
            width: 60px;
            height: 60px;
            animation: spin 1s linear infinite;
            margin-bottom: 30px;
        }

        @keyframes spin {
            0% {
                transform: rotate(0deg);
            }

            100% {
                transform: rotate(360deg);
            }
        }

        #instructions {
            position: fixed;
            top: 20px;
            left: 20px;
            right: 20px;
            background: rgba(0, 0, 0, 0.9);
            color: white;
            padding: 15px;
            border-radius: 12px;
            z-index: 100;
            font-size: 14px;
            backdrop-filter: blur(15px);
        }

        #instructions h3 {
            margin: 0 0 10px 0;
            color: #4ECDC4;
        }

        #imageList {
            position: fixed;
            bottom: 20px;
            left: 20px;
            right: 20px;
            background: rgba(0, 0, 0, 0.9);
            color: white;
            padding: 15px;
            border-radius: 12px;
            z-index: 100;
            max-height: 180px;
            overflow-y: auto;
            backdrop-filter: blur(15px);
        }

        .image-item {
            display: flex;
            align-items: center;
            padding: 10px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-bottom: 8px;
            border: 2px solid transparent;
        }

        .image-item:hover {
            background: rgba(255, 255, 255, 0.2);
            border-color: #4ECDC4;
        }

        .image-item.active {
            border-color: #4ECDC4;
            background: rgba(78, 205, 196, 0.2);
        }

        .image-item img {
            width: 60px;
            height: 45px;
            object-fit: cover;
            border-radius: 6px;
            margin-right: 12px;
        }

        .image-info {
            flex: 1;
            font-size: 13px;
        }

        .image-author {
            font-weight: bold;
            color: #ffffff;
            margin-bottom: 3px;
        }

        .image-id {
            color: #cccccc;
            font-size: 12px;
        }

        .pattern-info {
            color: #4ECDC4;
            font-size: 11px;
            margin-top: 3px;
        }

        #toggleList {
            position: fixed;
            bottom: 220px;
            right: 20px;
            background: linear-gradient(135deg, #4ECDC4, #44A08D);
            color: white;
            border: none;
            padding: 12px;
            border-radius: 50%;
            cursor: pointer;
            z-index: 101;
            font-size: 18px;
            width: 50px;
            height: 50px;
            box-shadow: 0 4px 20px rgba(78, 205, 196, 0.4);
        }
    </style>
</head>

<body>
    <div id="loading">
        <div class="spinner"></div>
        <p>Đang tải hình ảnh và tạo NFT targets...</p>
        <p>Đang kiểm tra thư mục targets/</p>
    </div>

    <div id="instructions">
        <h3>🎯 NFT Image Tracking</h3>
        <p>1. Đảm bảo thư mục <code>targets/</code> chứa files .fset, .fset3, .iset</p>
        <p>2. Chọn hình ảnh từ danh sách (highlight màu xanh)</p>
        <p>3. Click để mở full-size và in ra giấy</p>
        <p>4. Hướng camera vào chính hình ảnh đã in</p>
        <p>5. NFT sẽ track và hiển thị AR content!</p>
        <p><strong>💡 Mẹo:</strong> In kích thước A5, ánh sáng đủ, giữ ổn định</p>
    </div>

    <button id="toggleList">📋</button>

    <div id="imageList">
        <h4>📸 Hình ảnh có thể tracking:</h4>
        <div id="imageContainer">
            Đang tải...
        </div>
    </div>

    <a-scene vr-mode-ui="enabled: false"
        renderer="logarithmicDepthBuffer: true; colorManagement: true; sortObjects: true;" embedded
        arjs="trackingMethod: best; sourceType: webcam; debugUIEnabled: true;">

        <a-assets id="assets">
            <!-- Assets sẽ được thêm động -->
        </a-assets>

        <!-- NFT entities sẽ được thêm động -->

        <a-entity camera></a-entity>
    </a-scene>

    <script>
        class RealARTracker {
            constructor() {
                this.apiUrl = 'https://picsum.photos/v2/list';
                this.images = [];
                this.scene = null;
                this.assets = null;
                this.currentMarkers = [];
                this.maxImages = 4; // Giới hạn để tránh quá tải
                this.selectedImageIndex = 0;
                this.patternUrls = [
                    'https://raw.githubusercontent.com/AR-js-org/AR.js/master/data/data/patt.hiro',
                    'https://raw.githubusercontent.com/AR-js-org/AR.js/master/data/data/patt.kanji',
                    // Thêm patterns khác nếu có
                ];

                this.init();
            }

            async init() {
                try {
                    this.scene = document.querySelector('a-scene');
                    this.assets = document.querySelector('#assets');

                    // Fetch hình ảnh từ API
                    await this.fetchImages();

                    // Tạo AR markers với patterns
                    await this.createARMarkers();

                    // Setup UI
                    this.setupUI();

                    // Ẩn loading screen
                    this.hideLoading();

                } catch (error) {
                    console.error('Lỗi khởi tạo AR:', error);
                    this.showError('Có lỗi xảy ra khi khởi tạo AR. Vui lòng thử lại.');
                }
            }

            async fetchImages() {
                try {
                    console.log('Đang fetch hình ảnh từ API...');
                    const response = await fetch(this.apiUrl);

                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }

                    const allImages = await response.json();

                    // Chọn hình ảnh có kích thước phù hợp cho tracking
                    this.images = allImages
                        .filter(img => img.width >= 400 && img.height >= 300)
                        .slice(0, this.maxImages);

                    console.log(`Đã tải ${this.images.length} hình ảnh`);

                    // Hiển thị danh sách hình ảnh
                    this.displayImageList();

                } catch (error) {
                    console.error('Lỗi khi fetch hình ảnh:', error);
                    throw error;
                }
            }

            displayImageList() {
                const container = document.getElementById('imageContainer');
                container.innerHTML = '';

                this.images.forEach((image, index) => {
                    const imageItem = document.createElement('div');
                    imageItem.className = 'image-item';
                    if (index === this.selectedImageIndex) {
                        imageItem.classList.add('active');
                    }

                    const thumbnailUrl = `https://picsum.photos/id/${image.id}/200/150`;

                    imageItem.innerHTML = `
                        <img src="${thumbnailUrl}" alt="Image ${image.id}" loading="lazy">
                        <div class="image-info">
                            <div class="image-author">📷 ${image.author}</div>
                            <div class="image-id">ID: ${image.id} | ${image.width}x${image.height}</div>
                            <div class="pattern-info">
                                🎯 Pattern ${index + 1} - Click để chọn và mở
                            </div>
                        </div>
                    `;

                    // Click để chọn hình ảnh và mở full-size
                    imageItem.addEventListener('click', () => {
                        this.selectImage(index);
                        this.openImageFullSize(image);
                    });

                    container.appendChild(imageItem);
                });
            }

            selectImage(index) {
                this.selectedImageIndex = index;

                // Update UI
                document.querySelectorAll('.image-item').forEach((item, i) => {
                    if (i === index) {
                        item.classList.add('active');
                    } else {
                        item.classList.remove('active');
                    }
                });

                console.log(`Đã chọn hình ảnh: ${this.images[index].author} (ID: ${this.images[index].id})`);
            }

            openImageFullSize(image) {
                const fullImageUrl = `https://picsum.photos/id/${image.id}/800/600`;
                window.open(fullImageUrl, '_blank');
            }

            async createARMarkers() {
                console.log('Đang tạo AR markers...');

                for (let i = 0; i < this.images.length; i++) {
                    const image = this.images[i];
                    await this.createMarkerForImage(image, i);
                }

                console.log('Đã tạo xong tất cả AR markers');
            }

            async createMarkerForImage(image, index) {
                const imageId = image.id;
                const imageUrl = `https://picsum.photos/id/${imageId}/400/300`;

                // Tạo asset cho hình ảnh
                const imgAsset = document.createElement('img');
                imgAsset.id = `img-${imageId}`;
                imgAsset.src = imageUrl;
                imgAsset.crossOrigin = 'anonymous';
                this.assets.appendChild(imgAsset);

                // Đợi hình ảnh load xong
                await new Promise((resolve) => {
                    imgAsset.onload = resolve;
                    imgAsset.onerror = resolve;
                });

                // Tạo NFT entity thay vì marker
                const nftEntity = document.createElement('a-nft');
                nftEntity.setAttribute('type', 'nft');
                nftEntity.setAttribute('url', `./targets/${imageId}`); // Sử dụng targets từ folder
                nftEntity.setAttribute('smooth', 'true');
                nftEntity.setAttribute('smoothCount', '10');
                nftEntity.setAttribute('smoothTolerance', '.01');
                nftEntity.setAttribute('smoothThreshold', '5');
                nftEntity.setAttribute('id', `nft-${imageId}`);

                // Tạo AR content
                const arContent = this.createARContent(image, imageId);
                nftEntity.appendChild(arContent);

                // Event listeners cho NFT
                nftEntity.addEventListener('markerFound', () => {
                    console.log(`🎯 NFT tracking thành công: ${image.author} (ID: ${imageId})`);
                    this.onMarkerFound(imageId, image);
                });

                nftEntity.addEventListener('markerLost', () => {
                    console.log(`❌ Mất NFT tracking: ${image.author} (ID: ${imageId})`);
                    this.onMarkerLost(imageId);
                });

                this.scene.appendChild(nftEntity);
                this.currentMarkers.push(nftEntity);

                console.log(`✅ Đã tạo NFT target cho: ${image.author} (ID: ${imageId})`);
                console.log(`📁 Đang tìm targets tại: ./targets/${imageId}.fset, .fset3, .iset`);

                // Kiểm tra targets có tồn tại không
                await this.checkTargetFiles(imageId);
            }

            async checkTargetFiles(imageId) {
                const extensions = ['fset', 'fset3', 'iset'];
                const results = [];

                for (const ext of extensions) {
                    try {
                        const response = await fetch(`./targets/${imageId}.${ext}`);
                        results.push({
                            file: `${imageId}.${ext}`,
                            exists: response.ok,
                            status: response.status
                        });
                    } catch (error) {
                        results.push({
                            file: `${imageId}.${ext}`,
                            exists: false,
                            error: error.message
                        });
                    }
                }

                // Log kết quả
                console.log(`📁 Target files cho ${imageId}:`);
                results.forEach(result => {
                    if (result.exists) {
                        console.log(`  ✅ ${result.file} - OK`);
                    } else {
                        console.log(`  ❌ ${result.file} - Missing (${result.status || result.error})`);
                    }
                });

                const allExist = results.every(r => r.exists);
                if (!allExist) {
                    console.warn(`⚠️ Một số target files cho ${imageId} không tồn tại. NFT tracking có thể không hoạt động.`);
                }

                return allExist;
            }

            async generatePatternFromImage(imgElement) {
                try {
                    // Tạo canvas để xử lý hình ảnh
                    const canvas = document.createElement('canvas');
                    const ctx = canvas.getContext('2d');
                    canvas.width = 16;
                    canvas.height = 16;

                    // Vẽ hình ảnh xuống canvas nhỏ
                    ctx.drawImage(imgElement, 0, 0, 16, 16);

                    // Chuyển thành grayscale và tăng contrast
                    const imageData = ctx.getImageData(0, 0, 16, 16);
                    const data = imageData.data;

                    for (let i = 0; i < data.length; i += 4) {
                        // Convert to grayscale
                        const gray = data[i] * 0.299 + data[i + 1] * 0.587 + data[i + 2] * 0.114;
                        // Increase contrast
                        const contrast = gray > 128 ? 255 : 0;

                        data[i] = contrast;     // Red
                        data[i + 1] = contrast; // Green
                        data[i + 2] = contrast; // Blue
                    }

                    ctx.putImageData(imageData, 0, 0);

                    // Tạo pattern data URL
                    return canvas.toDataURL('image/png');

                } catch (error) {
                    console.error('Lỗi tạo pattern:', error);
                    return null;
                }
            }

            createARContent(image, imageId) {
                const group = document.createElement('a-entity');
                group.setAttribute('id', `ar-content-${imageId}`);

                // Hiển thị hình ảnh gốc
                const imagePlane = document.createElement('a-plane');
                imagePlane.setAttribute('position', '0 0 0.1');
                imagePlane.setAttribute('width', '2');
                imagePlane.setAttribute('height', '1.5');
                imagePlane.setAttribute('material', `src: #img-${imageId}; transparent: true; opacity: 0.8`);
                imagePlane.setAttribute('animation', 'property: rotation; to: 0 0 5; dir: alternate; loop: true; dur: 4000');

                // Text thông tin tác giả
                const authorText = document.createElement('a-text');
                authorText.setAttribute('value', `📷 ${image.author}`);
                authorText.setAttribute('position', '0 1.2 0');
                authorText.setAttribute('align', 'center');
                authorText.setAttribute('color', '#ffffff');
                authorText.setAttribute('scale', '1.5 1.5 1.5');
                authorText.setAttribute('geometry', 'primitive: plane; width: auto; height: auto');
                authorText.setAttribute('material', 'color: rgba(0,0,0,0.8)');

                // ID text
                const idText = document.createElement('a-text');
                idText.setAttribute('value', `ID: ${imageId}`);
                idText.setAttribute('position', '0 0.8 0');
                idText.setAttribute('align', 'center');
                idText.setAttribute('color', '#4ECDC4');
                idText.setAttribute('scale', '1.2 1.2 1.2');

                // 3D elements
                const box = document.createElement('a-box');
                box.setAttribute('position', '1 0.5 0.2');
                box.setAttribute('width', '0.3');
                box.setAttribute('height', '0.3');
                box.setAttribute('depth', '0.3');
                box.setAttribute('material', 'color: #4ECDC4; opacity: 0.8');
                box.setAttribute('animation', 'property: rotation; to: 0 360 0; loop: true; dur: 3000');

                const sphere = document.createElement('a-sphere');
                sphere.setAttribute('position', '-1 0.5 0.2');
                sphere.setAttribute('radius', '0.15');
                sphere.setAttribute('material', 'color: #FF6B6B; opacity: 0.8');
                sphere.setAttribute('animation', 'property: position; to: -1 1.2 0.2; dir: alternate; loop: true; dur: 2000');

                // Border frame
                const border = document.createElement('a-box');
                border.setAttribute('position', '0 0 -0.05');
                border.setAttribute('width', '2.2');
                border.setAttribute('height', '1.7');
                border.setAttribute('depth', '0.02');
                border.setAttribute('material', 'color: #4ECDC4; opacity: 0.3');

                group.appendChild(imagePlane);
                group.appendChild(authorText);
                group.appendChild(idText);
                group.appendChild(box);
                group.appendChild(sphere);
                group.appendChild(border);

                return group;
            }

            onMarkerFound(imageId, image) {
                // Hiển thị thông báo
                this.showNotification(`🎯 Tracking: ${image.author}`, 'success');

                // Haptic feedback
                if (navigator.vibrate) {
                    navigator.vibrate(100);
                }
            }

            onMarkerLost(imageId) {
                this.showNotification('❌ Mất tracking', 'warning');
            }

            showNotification(message, type = 'info') {
                // Tạo notification
                const notification = document.createElement('div');
                notification.style.cssText = `
                    position: fixed;
                    top: 50%;
                    left: 50%;
                    transform: translate(-50%, -50%);
                    background: ${type === 'success' ? '#27ae60' : type === 'warning' ? '#f39c12' : '#3498db'};
                    color: white;
                    padding: 15px 25px;
                    border-radius: 25px;
                    z-index: 1001;
                    font-weight: bold;
                    box-shadow: 0 4px 15px rgba(0,0,0,0.3);
                    animation: fadeInOut 2s ease-in-out;
                `;
                notification.textContent = message;

                document.body.appendChild(notification);

                setTimeout(() => {
                    if (notification.parentNode) {
                        notification.parentNode.removeChild(notification);
                    }
                }, 2000);
            }

            hideLoading() {
                document.getElementById('loading').classList.add('hidden');
            }

            showError(message) {
                const loading = document.getElementById('loading');
                loading.innerHTML = `
                    <div style="color: #ff6b6b; text-align: center;">
                        <h3>❌ Lỗi</h3>
                        <p>${message}</p>
                        <button onclick="location.reload()" style="padding: 10px 20px; margin-top: 20px; background: #3498db; color: white; border: none; border-radius: 5px; cursor: pointer;">
                            Thử lại
                        </button>
                    </div>
                `;
            }

            setupUI() {
                const toggleButton = document.getElementById('toggleList');
                const imageList = document.getElementById('imageList');
                let isListVisible = true;

                toggleButton.addEventListener('click', () => {
                    isListVisible = !isListVisible;
                    imageList.style.display = isListVisible ? 'block' : 'none';
                    toggleButton.textContent = isListVisible ? '📋' : '👁️';
                });
            }
        }

        // Khởi tạo ứng dụng khi DOM đã sẵn sàng
        document.addEventListener('DOMContentLoaded', () => {
            new RealARTracker();
        });

        // CSS cho animation
        const style = document.createElement('style');
        style.textContent = `
            @keyframes fadeInOut {
                0% { opacity: 0; transform: translate(-50%, -50%) scale(0.8); }
                20% { opacity: 1; transform: translate(-50%, -50%) scale(1.05); }
                25% { transform: translate(-50%, -50%) scale(1); }
                80% { opacity: 1; transform: translate(-50%, -50%) scale(1); }
                100% { opacity: 0; transform: translate(-50%, -50%) scale(0.8); }
            }
        `;
        document.head.appendChild(style);
    </script>
</body>

</html>