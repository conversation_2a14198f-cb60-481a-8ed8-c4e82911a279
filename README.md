# AR Image Tracking với <PERSON>um Photos

Một ứng dụng Web AR sử dụng A-Frame và AR.js để tracking hình ảnh từ API Picsum Photos và hiển thị nội dung AR trên các hình ảnh được track.

## 🌟 Tính năng

- **Tự động tải hình ảnh**: L<PERSON>y hình ảnh ngẫu nhiên từ Picsum Photos API
- **AR Image Tracking**: Tracking hình ảnh thực tế và hiển thị nội dung AR
- **Giao diện thân thiện**: UI responsive với dark theme
- **Thông báo trực quan**: Hiệu ứng và âm thanh khi tracking thành công
- **Tương tác đa nền tảng**: Hoạt động trên cả desktop và mobile

## 🚀 Cách sử dụng

### 1. Khởi chạy ứng dụng
- Mở file `index.html` trong trình duyệt web
- Cho phép truy cập camera khi được yêu cầu
- <PERSON><PERSON><PERSON> ứng dụng tải hình ảnh từ API

### 2. <PERSON><PERSON><PERSON> bị hình ảnh để tracking
- Xem danh sách hình ảnh ở phía dưới màn hình
- Click vào một hình ảnh để mở phiên bản full-size
- In hình ảnh ra giấy hoặc hiển thị trên thiết bị khác

### 3. Thực hiện AR tracking
- Hướng camera vào hình ảnh đã in/hiển thị
- Khi tracking thành công, bạn sẽ thấy:
  - Nội dung AR 3D xuất hiện trên hình ảnh
  - Thông báo "🎯 Đang tracking"
  - Âm thanh xác nhận
  - Hiệu ứng animation 3D

## 🎮 Điều khiển

- **📋 Button**: Ẩn/hiện danh sách hình ảnh
- **Double tap/click**: Ẩn/hiện hướng dẫn sử dụng
- **Click hình ảnh**: Mở hình ảnh full-size trong tab mới

## 🛠️ Cấu trúc dự án

```
BEProcessCaptured/
├── index.html          # File HTML chính
├── css/
│   └── styles.css      # Stylesheet chính
├── js/
│   └── ar-app.js       # Logic ứng dụng AR
└── README.md           # Tài liệu hướng dẫn
```

## 🔧 Công nghệ sử dụng

- **A-Frame**: Framework WebVR/AR
- **AR.js**: Thư viện AR cho web
- **Picsum Photos API**: Nguồn hình ảnh ngẫu nhiên
- **Web Audio API**: Tạo âm thanh thông báo
- **CSS3**: Animations và responsive design

## 📱 Tương thích

### Trình duyệt được hỗ trợ:
- ✅ Chrome (Android/Desktop)
- ✅ Firefox (Android/Desktop)  
- ✅ Safari (iOS/macOS)
- ✅ Edge (Desktop)

### Yêu cầu:
- Camera (webcam hoặc camera điện thoại)
- HTTPS hoặc localhost (để truy cập camera)
- JavaScript được bật

## 🎯 Cách hoạt động

1. **Tải dữ liệu**: Ứng dụng fetch danh sách hình ảnh từ Picsum API
2. **Tạo targets**: Mỗi hình ảnh được tạo thành một AR marker
3. **Camera tracking**: AR.js sử dụng camera để detect hình ảnh
4. **Render AR**: Khi detect thành công, hiển thị nội dung 3D

## 🔍 Troubleshooting

### Camera không hoạt động:
- Đảm bảo truy cập qua HTTPS hoặc localhost
- Cho phép quyền truy cập camera
- Thử refresh trang

### Tracking không chính xác:
- Đảm bảo hình ảnh có đủ ánh sáng
- Giữ camera ổn định
- Hình ảnh cần rõ nét và không bị mờ
- Khoảng cách phù hợp (20-50cm)

### Performance kém:
- Giảm số lượng hình ảnh tracking (sửa `maxImages` trong code)
- Sử dụng thiết bị có camera tốt hơn
- Đóng các tab khác để giải phóng RAM

## 🎨 Tùy chỉnh

### Thay đổi số lượng hình ảnh:
```javascript
// Trong js/ar-app.js
this.maxImages = 8; // Thay đổi số này
```

### Tùy chỉnh nội dung AR:
```javascript
// Sửa hàm createARContent() để thay đổi hiệu ứng 3D
```

### Thay đổi màu sắc:
```css
/* Trong css/styles.css */
/* Sửa các biến màu trong :root hoặc các class */
```

## 📄 License

Dự án này được phát hành dưới MIT License. Bạn có thể tự do sử dụng, sửa đổi và phân phối.

## 🤝 Đóng góp

Mọi đóng góp đều được chào đón! Hãy tạo issue hoặc pull request nếu bạn có ý tưởng cải thiện.

## 📞 Hỗ trợ

Nếu gặp vấn đề, hãy:
1. Kiểm tra phần Troubleshooting ở trên
2. Mở Developer Console để xem lỗi
3. Tạo issue với thông tin chi tiết về lỗi

---

**Chúc bạn có trải nghiệm AR thú vị! 🎉**
