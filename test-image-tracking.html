<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no, viewport-fit=cover">
    <title>Test Image Tracking</title>
    <script src="https://aframe.io/releases/1.4.0/aframe.min.js"></script>
    <script src="https://cdn.jsdelivr.net/gh/AR-js-org/AR.js@3.4.5/aframe/build/aframe-ar-nft.js"></script>
    <style>
        body {
            margin: 0;
            font-family: Arial, sans-serif;
            background: #000;
        }
        
        #info {
            position: fixed;
            top: 10px;
            left: 10px;
            right: 10px;
            background: rgba(0, 0, 0, 0.9);
            color: white;
            padding: 15px;
            border-radius: 10px;
            z-index: 100;
            font-size: 14px;
        }
        
        #info h3 {
            margin: 0 0 10px 0;
            color: #4ECDC4;
        }
        
        .test-images {
            display: flex;
            gap: 10px;
            margin: 10px 0;
            flex-wrap: wrap;
        }
        
        .test-image {
            width: 80px;
            height: 60px;
            border: 2px solid #4ECDC4;
            border-radius: 5px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .test-image:hover {
            transform: scale(1.1);
            box-shadow: 0 0 10px #4ECDC4;
        }
        
        #status {
            position: fixed;
            bottom: 10px;
            left: 10px;
            right: 10px;
            background: rgba(0, 0, 0, 0.9);
            color: white;
            padding: 15px;
            border-radius: 10px;
            z-index: 100;
            font-size: 14px;
        }
        
        .success { color: #4CAF50; }
        .error { color: #f44336; }
        .warning { color: #ff9800; }
    </style>
</head>
<body>
    <div id="info">
        <h3>🎯 Test Image Tracking</h3>
        <p>Click vào hình ảnh bên dưới để mở full-size, sau đó in ra và test tracking:</p>
        <div class="test-images">
            <img src="https://picsum.photos/id/1/400/300" class="test-image" onclick="openImage('https://picsum.photos/id/1/800/600')" alt="Test Image 1">
            <img src="https://picsum.photos/id/10/400/300" class="test-image" onclick="openImage('https://picsum.photos/id/10/800/600')" alt="Test Image 2">
            <img src="https://picsum.photos/id/20/400/300" class="test-image" onclick="openImage('https://picsum.photos/id/20/800/600')" alt="Test Image 3">
        </div>
        <p><strong>Hướng dẫn:</strong></p>
        <p>1. Click hình ảnh → Mở tab mới</p>
        <p>2. In hình ảnh kích thước A5 (15x10cm)</p>
        <p>3. Hướng camera vào hình ảnh in</p>
    </div>
    
    <div id="status">
        <div id="tracking-status">📷 Đang khởi tạo camera...</div>
    </div>

    <a-scene 
        vr-mode-ui="enabled: false"
        renderer="logarithmicDepthBuffer: true"
        embedded
        arjs="trackingMethod: best; sourceType: webcam; debugUIEnabled: true;">
        
        <a-assets>
            <img id="test1" src="https://picsum.photos/id/1/400/300" crossorigin="anonymous">
            <img id="test2" src="https://picsum.photos/id/10/400/300" crossorigin="anonymous">
            <img id="test3" src="https://picsum.photos/id/20/400/300" crossorigin="anonymous">
        </a-assets>
        
        <!-- NFT Markers cho từng hình ảnh -->
        <!-- Lưu ý: Trong thực tế cần tạo NFT descriptors -->
        
        <!-- Fallback: Sử dụng pattern markers với custom patterns -->
        <a-marker type="pattern" preset="hiro" id="marker1">
            <a-plane 
                position="0 0 0" 
                width="2" 
                height="1.5" 
                material="src: #test1; transparent: true; opacity: 0.9"
                animation="property: rotation; to: 0 5 0; dir: alternate; loop: true; dur: 4000">
            </a-plane>
            
            <a-text 
                value="IMAGE 1 DETECTED!" 
                position="0 1.2 0" 
                align="center" 
                color="white"
                scale="1.5 1.5 1.5"
                geometry="primitive: plane; width: auto; height: auto"
                material="color: rgba(0,0,0,0.7)">
            </a-text>
            
            <a-box
                position="0 0.5 0.5"
                width="0.5"
                height="0.5"
                depth="0.5"
                material="color: #FF6B6B;"
                animation="property: rotation; to: 0 360 0; loop: true; dur: 3000">
            </a-box>
        </a-marker>

        <a-marker type="pattern" preset="kanji" id="marker2">
            <a-plane 
                position="0 0 0" 
                width="2" 
                height="1.5" 
                material="src: #test2; transparent: true; opacity: 0.9"
                animation="property: rotation; to: 0 -5 0; dir: alternate; loop: true; dur: 4000">
            </a-plane>
            
            <a-text 
                value="IMAGE 2 DETECTED!" 
                position="0 1.2 0" 
                align="center" 
                color="white"
                scale="1.5 1.5 1.5"
                geometry="primitive: plane; width: auto; height: auto"
                material="color: rgba(0,0,0,0.7)">
            </a-text>
            
            <a-cylinder
                position="0 0.5 0.5"
                radius="0.3"
                height="0.8"
                material="color: #4ECDC4;"
                animation="property: rotation; to: 360 0 0; loop: true; dur: 4000">
            </a-cylinder>
        </a-marker>

        <!-- Camera -->
        <a-entity camera></a-entity>
    </a-scene>

    <script>
        const statusDiv = document.getElementById('tracking-status');
        
        function openImage(url) {
            window.open(url, '_blank');
        }
        
        // Đợi A-Frame load xong
        document.querySelector('a-scene').addEventListener('loaded', () => {
            statusDiv.innerHTML = '<span class="success">✅ AR đã sẵn sàng! Hướng camera vào hình ảnh đã in.</span>';
            
            // Event listeners cho markers
            const marker1 = document.getElementById('marker1');
            const marker2 = document.getElementById('marker2');
            
            marker1.addEventListener('markerFound', () => {
                statusDiv.innerHTML = '<span class="success">🎯 Đã phát hiện HÌNH ẢNH 1!</span>';
                console.log('Image 1 detected!');
                
                if (navigator.vibrate) {
                    navigator.vibrate(100);
                }
            });
            
            marker1.addEventListener('markerLost', () => {
                statusDiv.innerHTML = '<span class="warning">❌ Mất tracking hình ảnh 1</span>';
                console.log('Image 1 lost!');
            });
            
            marker2.addEventListener('markerFound', () => {
                statusDiv.innerHTML = '<span class="success">🎯 Đã phát hiện HÌNH ẢNH 2!</span>';
                console.log('Image 2 detected!');
                
                if (navigator.vibrate) {
                    navigator.vibrate([100, 50, 100]);
                }
            });
            
            marker2.addEventListener('markerLost', () => {
                statusDiv.innerHTML = '<span class="warning">❌ Mất tracking hình ảnh 2</span>';
                console.log('Image 2 lost!');
            });
        });
        
        // Timeout fallback
        setTimeout(() => {
            if (statusDiv.innerHTML.includes('Đang khởi tạo')) {
                statusDiv.innerHTML = '<span class="error">⚠️ AR khởi tạo chậm, vui lòng đợi...</span>';
            }
        }, 5000);
        
        // Log AR.js events
        window.addEventListener('arjs-video-loaded', () => {
            console.log('AR.js video loaded');
            statusDiv.innerHTML = '<span class="success">✅ Camera đã sẵn sàng!</span>';
        });
        
        // Global error handling
        window.addEventListener('error', (e) => {
            console.error('Global error:', e.error);
        });
    </script>
</body>
</html>
