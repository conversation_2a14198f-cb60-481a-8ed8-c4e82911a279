<!DOCTYPE html>
<html lang="vi">

<head>
    <meta charset="UTF-8">
    <meta name="viewport"
        content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no, viewport-fit=cover">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="theme-color" content="#000000">

    <title>AR Image Tracking - Picsum Photos</title>
    <meta name="description" content="AR Image Tracking với hình ảnh từ Picsum Photos API">
    <meta name="keywords" content="AR, Augmented Reality, Image Tracking, WebAR, A-Frame">

    <!-- Favicon -->
    <link rel="icon"
        href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>📷</text></svg>">

    <!-- CSS -->
    <link rel="stylesheet" href="css/styles.css">

    <!-- A-Frame and AR.js -->
    <script src="https://aframe.io/releases/1.4.0/aframe.min.js"></script>
    <script src="https://cdn.jsdelivr.net/gh/AR-js-org/AR.js@3.4.5/aframe/build/aframe-ar.js"></script>
</head>

<body>
    <div id="loading">
        <div class="spinner"></div>
        <p>Đang tải hình ảnh từ API...</p>
        <p>Vui lòng chờ trong giây lát</p>
    </div>

    <div id="instructions">
        <h3>🎯 Hướng dẫn sử dụng AR Image Tracking</h3>
        <p>1. Cho phép truy cập camera khi được yêu cầu</p>
        <p>2. Chọn một hình ảnh từ danh sách bên dưới</p>
        <p>3. Mở hình ảnh đó trên thiết bị khác hoặc in ra</p>
        <p>4. Hướng camera vào hình ảnh để xem nội dung AR</p>
    </div>

    <button id="toggleList">📋</button>

    <div id="imageList">
        <h4>📸 Danh sách hình ảnh có thể tracking:</h4>
        <div id="imageContainer">
            Đang tải...
        </div>
    </div>

    <a-scene vr-mode-ui="enabled: false"
        renderer="logarithmicDepthBuffer: true; colorManagement: true; sortObjects: true;" embedded
        arjs="trackingMethod: best; sourceType: webcam; debugUIEnabled: true;">

        <a-assets>
            <!-- Các assets sẽ được thêm động bởi JavaScript -->
        </a-assets>

        <!-- Markers sẽ được thêm động ở đây bởi JavaScript -->

        <a-entity camera></a-entity>
    </a-scene>

    <script src="js/ar-app.js"></script>
</body>

</html>