<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AR Image Tracking - Picsum Photos</title>
    <script src="https://aframe.io/releases/1.4.0/aframe.min.js"></script>
    <script src="https://cdn.jsdelivr.net/gh/AR-js-org/AR.js@3.4.5/aframe/build/aframe-ar-nft.js"></script>
    <style>
        body {
            margin: 0;
            font-family: Arial, sans-serif;
        }
        
        #loading {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            z-index: 1000;
        }
        
        #loading.hidden {
            display: none;
        }
        
        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #3498db;
            border-radius: 50%;
            width: 50px;
            height: 50px;
            animation: spin 2s linear infinite;
            margin-bottom: 20px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        #instructions {
            position: fixed;
            top: 20px;
            left: 20px;
            right: 20px;
            background: rgba(0, 0, 0, 0.7);
            color: white;
            padding: 15px;
            border-radius: 10px;
            z-index: 100;
            font-size: 14px;
        }
        
        #imageList {
            position: fixed;
            bottom: 20px;
            left: 20px;
            right: 20px;
            background: rgba(0, 0, 0, 0.7);
            color: white;
            padding: 15px;
            border-radius: 10px;
            z-index: 100;
            max-height: 200px;
            overflow-y: auto;
        }
        
        .image-item {
            display: flex;
            align-items: center;
            margin-bottom: 10px;
            padding: 5px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 5px;
        }
        
        .image-item img {
            width: 60px;
            height: 40px;
            object-fit: cover;
            border-radius: 3px;
            margin-right: 10px;
        }
        
        .image-info {
            flex: 1;
            font-size: 12px;
        }
        
        .image-author {
            font-weight: bold;
            margin-bottom: 2px;
        }
        
        .image-id {
            opacity: 0.7;
        }
        
        #toggleList {
            position: fixed;
            bottom: 240px;
            right: 20px;
            background: #3498db;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 50px;
            cursor: pointer;
            z-index: 101;
        }
    </style>
</head>
<body>
    <div id="loading">
        <div class="spinner"></div>
        <p>Đang tải hình ảnh từ API...</p>
        <p>Vui lòng chờ trong giây lát</p>
    </div>
    
    <div id="instructions">
        <h3>🎯 Hướng dẫn sử dụng AR Image Tracking</h3>
        <p>1. Cho phép truy cập camera khi được yêu cầu</p>
        <p>2. Chọn một hình ảnh từ danh sách bên dưới</p>
        <p>3. Mở hình ảnh đó trên thiết bị khác hoặc in ra</p>
        <p>4. Hướng camera vào hình ảnh để xem nội dung AR</p>
    </div>
    
    <button id="toggleList">📋</button>
    
    <div id="imageList">
        <h4>📸 Danh sách hình ảnh có thể tracking:</h4>
        <div id="imageContainer">
            Đang tải...
        </div>
    </div>

    <a-scene
        vr-mode-ui="enabled: false"
        renderer="logarithmicDepthBuffer: true;"
        embedded
        arjs="trackingMethod: best; sourceType: webcam; debugUIEnabled: false;">
        
        <a-assets>
            <!-- Các assets sẽ được thêm động -->
        </a-assets>

        <!-- Markers sẽ được thêm động ở đây -->

        <a-entity camera></a-entity>
    </a-scene>

    <script src="js/ar-app.js"></script>
</body>
</html>
