<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no, viewport-fit=cover">
    <title>Test AR Markers</title>
    <script src="https://aframe.io/releases/1.4.0/aframe.min.js"></script>
    <script src="https://cdn.jsdelivr.net/gh/AR-js-org/AR.js@3.4.5/aframe/build/aframe-ar.js"></script>
    <style>
        body {
            margin: 0;
            font-family: Arial, sans-serif;
            background: #000;
        }
        
        #info {
            position: fixed;
            top: 10px;
            left: 10px;
            right: 10px;
            background: rgba(0, 0, 0, 0.9);
            color: white;
            padding: 15px;
            border-radius: 10px;
            z-index: 100;
            font-size: 14px;
        }
        
        #info h3 {
            margin: 0 0 10px 0;
            color: #4ECDC4;
        }
        
        #info a {
            color: #4ECDC4;
            text-decoration: none;
            font-weight: bold;
        }
        
        #info a:hover {
            text-decoration: underline;
        }
        
        #status {
            position: fixed;
            bottom: 10px;
            left: 10px;
            right: 10px;
            background: rgba(0, 0, 0, 0.9);
            color: white;
            padding: 15px;
            border-radius: 10px;
            z-index: 100;
            font-size: 14px;
        }
        
        .success { color: #4CAF50; }
        .error { color: #f44336; }
        .warning { color: #ff9800; }
        
        .marker-links {
            display: flex;
            gap: 15px;
            margin: 10px 0;
            flex-wrap: wrap;
        }
        
        .marker-link {
            background: #4ECDC4;
            color: white;
            padding: 8px 15px;
            border-radius: 20px;
            text-decoration: none;
            font-size: 12px;
            font-weight: bold;
        }
        
        .marker-link:hover {
            background: #44A08D;
        }
    </style>
</head>
<body>
    <div id="info">
        <h3>🎯 Test AR Markers</h3>
        <p>Tải và in các markers này để test AR:</p>
        <div class="marker-links">
            <a href="https://ar-js-org.github.io/AR.js/data/images/hiro.png" target="_blank" class="marker-link">
                📄 Hiro Marker
            </a>
            <a href="https://ar-js-org.github.io/AR.js/data/images/kanji.png" target="_blank" class="marker-link">
                📄 Kanji Marker
            </a>
        </div>
        <p><strong>Cách sử dụng:</strong></p>
        <p>1. Click link trên để mở marker</p>
        <p>2. In marker ra giấy hoặc hiển thị trên màn hình khác</p>
        <p>3. Hướng camera vào marker để xem AR</p>
    </div>
    
    <div id="status">
        <div id="tracking-status">📷 Đang khởi tạo camera...</div>
    </div>

    <a-scene 
        vr-mode-ui="enabled: false"
        renderer="logarithmicDepthBuffer: true"
        embedded
        arjs="trackingMethod: best; sourceType: webcam; debugUIEnabled: true;">
        
        <!-- Hiro Marker -->
        <a-marker preset="hiro" id="hiro-marker">
            <!-- Nội dung AR cho Hiro -->
            <a-box 
                position="0 0.5 0" 
                material="color: #FF6B6B;" 
                animation="property: rotation; to: 0 360 0; loop: true; dur: 3000">
            </a-box>
            
            <a-text 
                value="HIRO MARKER" 
                position="0 1.5 0" 
                align="center" 
                color="white"
                scale="2 2 2">
            </a-text>
            
            <a-sphere
                position="1 0.5 0"
                radius="0.3"
                material="color: #4ECDC4;"
                animation="property: position; to: -1 0.5 0; dir: alternate; loop: true; dur: 2000">
            </a-sphere>
        </a-marker>

        <!-- Kanji Marker -->
        <a-marker preset="kanji" id="kanji-marker">
            <!-- Nội dung AR cho Kanji -->
            <a-cylinder 
                position="0 0.5 0" 
                radius="0.5"
                height="1"
                material="color: #96CEB4;" 
                animation="property: rotation; to: 360 0 0; loop: true; dur: 4000">
            </a-cylinder>
            
            <a-text 
                value="KANJI MARKER" 
                position="0 1.8 0" 
                align="center" 
                color="white"
                scale="1.8 1.8 1.8">
            </a-text>
            
            <a-torus
                position="0 0.5 1"
                radius="0.3"
                radius-tubular="0.1"
                material="color: #FFEAA7;"
                animation="property: rotation; to: 0 360 360; loop: true; dur: 5000">
            </a-torus>
        </a-marker>

        <!-- Camera -->
        <a-entity camera></a-entity>
    </a-scene>

    <script>
        const statusDiv = document.getElementById('tracking-status');
        
        // Đợi A-Frame load xong
        document.querySelector('a-scene').addEventListener('loaded', () => {
            statusDiv.innerHTML = '<span class="success">✅ AR đã sẵn sàng! Hướng camera vào marker.</span>';
            
            // Event listeners cho markers
            const hiroMarker = document.getElementById('hiro-marker');
            const kanjiMarker = document.getElementById('kanji-marker');
            
            hiroMarker.addEventListener('markerFound', () => {
                statusDiv.innerHTML = '<span class="success">🎯 Đã tìm thấy HIRO marker!</span>';
                console.log('Hiro marker found!');
                
                // Haptic feedback
                if (navigator.vibrate) {
                    navigator.vibrate(100);
                }
            });
            
            hiroMarker.addEventListener('markerLost', () => {
                statusDiv.innerHTML = '<span class="warning">❌ Mất Hiro marker</span>';
                console.log('Hiro marker lost!');
            });
            
            kanjiMarker.addEventListener('markerFound', () => {
                statusDiv.innerHTML = '<span class="success">🎯 Đã tìm thấy KANJI marker!</span>';
                console.log('Kanji marker found!');
                
                // Haptic feedback
                if (navigator.vibrate) {
                    navigator.vibrate([100, 50, 100]);
                }
            });
            
            kanjiMarker.addEventListener('markerLost', () => {
                statusDiv.innerHTML = '<span class="warning">❌ Mất Kanji marker</span>';
                console.log('Kanji marker lost!');
            });
        });
        
        // Timeout fallback
        setTimeout(() => {
            if (statusDiv.innerHTML.includes('Đang khởi tạo')) {
                statusDiv.innerHTML = '<span class="error">⚠️ AR khởi tạo chậm, vui lòng đợi...</span>';
            }
        }, 5000);
        
        // Log AR.js events
        window.addEventListener('arjs-video-loaded', () => {
            console.log('AR.js video loaded');
            statusDiv.innerHTML = '<span class="success">✅ Camera đã sẵn sàng!</span>';
        });
        
        window.addEventListener('camera-error', (e) => {
            console.error('Camera error event:', e);
            statusDiv.innerHTML = '<span class="error">❌ Lỗi camera</span>';
        });
        
        // Global error handling
        window.addEventListener('error', (e) => {
            console.error('Global error:', e.error);
        });
    </script>
</body>
</html>
