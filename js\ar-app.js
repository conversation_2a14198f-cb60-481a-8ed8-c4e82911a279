class ARImageTracker {
    constructor() {
        this.apiUrl = 'https://picsum.photos/v2/list';
        this.images = [];
        this.scene = null;
        this.assets = null;
        this.currentMarkers = [];
        // Điều chỉnh số lượng hình ảnh theo thiết bị
        this.maxImages = window.innerWidth <= 768 ? 6 : 8; // Mobile: 6, Desktop: 8

        this.init();
    }

    async init() {
        try {
            // Đợi A-Frame khởi tạo xong
            await this.waitForAFrame();

            this.scene = document.querySelector('a-scene');
            this.assets = document.querySelector('a-assets');

            // Fetch hình ảnh từ API
            await this.fetchImages();

            // Tạo image targets
            await this.createImageTargets();

            // Ẩn loading screen
            this.hideLoading();

            // Setup UI events
            this.setupUI();

        } catch (error) {
            console.error('Lỗi khởi tạo AR:', error);
            this.showError('<PERSON><PERSON> lỗi xảy ra khi khởi tạo AR. Vui lòng thử lại.');
        }
    }

    waitForAFrame() {
        return new Promise((resolve) => {
            if (document.querySelector('a-scene').hasLoaded) {
                resolve();
            } else {
                document.querySelector('a-scene').addEventListener('loaded', resolve);
            }
        });
    }

    async fetchImages() {
        try {
            console.log('Đang fetch hình ảnh từ API...');
            const response = await fetch(this.apiUrl);

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const allImages = await response.json();

            // Lấy một số hình ảnh ngẫu nhiên
            this.images = this.shuffleArray(allImages).slice(0, this.maxImages);

            console.log(`Đã tải ${this.images.length} hình ảnh`);

            // Hiển thị danh sách hình ảnh
            this.displayImageList();

        } catch (error) {
            console.error('Lỗi khi fetch hình ảnh:', error);
            throw error;
        }
    }

    shuffleArray(array) {
        const shuffled = [...array];
        for (let i = shuffled.length - 1; i > 0; i--) {
            const j = Math.floor(Math.random() * (i + 1));
            [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
        }
        return shuffled;
    }

    displayImageList() {
        const container = document.getElementById('imageContainer');
        container.innerHTML = '';

        this.images.forEach((image, index) => {
            const imageItem = document.createElement('div');
            imageItem.className = 'image-item';

            const thumbnailUrl = `https://picsum.photos/id/${image.id}/200/150`;

            imageItem.innerHTML = `
                <img src="${thumbnailUrl}" alt="Image ${image.id}" loading="lazy">
                <div class="image-info">
                    <div class="image-author">📷 ${image.author}</div>
                    <div class="image-id">ID: ${image.id} | ${image.width}x${image.height}</div>
                </div>
            `;

            // Thêm click event để mở hình ảnh full size
            imageItem.addEventListener('click', () => {
                this.openImageFullSize(image);
            });

            imageItem.style.cursor = 'pointer';
            container.appendChild(imageItem);
        });
    }

    openImageFullSize(image) {
        const fullImageUrl = `https://picsum.photos/id/${image.id}/800/600`;
        window.open(fullImageUrl, '_blank');
    }

    async createImageTargets() {
        console.log('Đang tạo image targets...');

        for (let i = 0; i < this.images.length; i++) {
            const image = this.images[i];
            await this.createSingleTarget(image, i);
        }

        console.log('Đã tạo xong tất cả image targets');
    }

    async createSingleTarget(image, index) {
        const imageId = image.id;
        const imageUrl = `https://picsum.photos/id/${imageId}/400/300`;

        // Tạo asset cho hình ảnh để hiển thị trong AR
        const imgAsset = document.createElement('img');
        imgAsset.id = `target-${imageId}`;
        imgAsset.src = imageUrl;
        imgAsset.crossOrigin = 'anonymous';
        this.assets.appendChild(imgAsset);

        // Đợi hình ảnh load xong
        await new Promise((resolve) => {
            imgAsset.onload = resolve;
            imgAsset.onerror = resolve;
        });

        // Sử dụng các preset markers có sẵn
        const presets = ['hiro', 'kanji'];
        const preset = presets[index % presets.length];

        // Tạo marker entity với preset
        const marker = document.createElement('a-marker');
        marker.setAttribute('preset', preset);
        marker.setAttribute('id', `marker-${imageId}`);
        marker.setAttribute('smooth', 'true');
        marker.setAttribute('smoothCount', '10');
        marker.setAttribute('smoothTolerance', '.01');
        marker.setAttribute('smoothThreshold', '5');

        // Tạo nội dung AR
        const arContent = this.createARContent(image, index);
        marker.appendChild(arContent);

        // Thêm event listeners
        marker.addEventListener('markerFound', () => {
            console.log(`🎯 Tìm thấy ${preset} marker cho hình ảnh: ${imageId}`);
            this.onMarkerFound(imageId);
        });

        marker.addEventListener('markerLost', () => {
            console.log(`❌ Mất ${preset} marker cho hình ảnh: ${imageId}`);
            this.onMarkerLost(imageId);
        });

        this.scene.appendChild(marker);
        this.currentMarkers.push(marker);

        // Log thông tin marker được tạo
        console.log(`Đã tạo ${preset} marker cho hình ảnh ${image.author} (ID: ${imageId})`);
    }

    generatePatternUrl(imageUrl) {
        // Trong thực tế, bạn cần tạo pattern file (.patt) từ hình ảnh
        // Ở đây tôi sử dụng một pattern mặc định
        return 'https://raw.githubusercontent.com/AR-js-org/AR.js/master/data/data/patt.hiro';
    }

    createARContent(image, index) {
        const group = document.createElement('a-entity');
        group.setAttribute('id', `ar-content-${image.id}`);

        // Tạo một hộp 3D với animation
        const box = document.createElement('a-box');
        box.setAttribute('position', '0 0.5 0');
        box.setAttribute('scale', '0.8 0.8 0.8');
        box.setAttribute('material', `color: ${this.getRandomColor()}; opacity: 0.8`);
        box.setAttribute('animation', 'property: rotation; to: 0 360 0; loop: true; dur: 8000');

        // Tạo text hiển thị thông tin tác giả
        const authorText = document.createElement('a-text');
        authorText.setAttribute('value', `📷 ${image.author}`);
        authorText.setAttribute('position', '0 1.2 0');
        authorText.setAttribute('align', 'center');
        authorText.setAttribute('color', '#ffffff');
        authorText.setAttribute('scale', '1.5 1.5 1.5');

        // Tạo text hiển thị ID
        const idText = document.createElement('a-text');
        idText.setAttribute('value', `ID: ${image.id}`);
        idText.setAttribute('position', '0 0.8 0');
        idText.setAttribute('align', 'center');
        idText.setAttribute('color', '#ffff00');
        idText.setAttribute('scale', '1.2 1.2 1.2');

        // Tạo sphere nhỏ xung quanh
        for (let i = 0; i < 3; i++) {
            const sphere = document.createElement('a-sphere');
            const angle = (i * 120) * Math.PI / 180;
            const x = Math.cos(angle) * 1.5;
            const z = Math.sin(angle) * 1.5;

            sphere.setAttribute('position', `${x} 0.5 ${z}`);
            sphere.setAttribute('radius', '0.1');
            sphere.setAttribute('material', `color: ${this.getRandomColor()}`);
            sphere.setAttribute('animation', `property: position; to: ${x} 1.2 ${z}; dir: alternate; loop: true; dur: 2000`);

            group.appendChild(sphere);
        }

        // Tạo hình ảnh 3D phía sau
        const plane = document.createElement('a-plane');
        plane.setAttribute('position', '0 0 -0.1');
        plane.setAttribute('width', '2');
        plane.setAttribute('height', '1.5');
        plane.setAttribute('material', `src: #target-${image.id}; transparent: true; opacity: 0.7`);

        group.appendChild(box);
        group.appendChild(authorText);
        group.appendChild(idText);
        group.appendChild(plane);

        return group;
    }

    getRandomColor() {
        const colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7', '#DDA0DD', '#98D8C8'];
        return colors[Math.floor(Math.random() * colors.length)];
    }

    onMarkerFound(imageId) {
        // Tìm thông tin hình ảnh
        const image = this.images.find(img => img.id == imageId);
        const imageName = image ? image.author : `ID: ${imageId}`;

        // Hiển thị thông báo
        this.showNotification(`🎯 Đang tracking: ${imageName}`, 'success');

        // Phát âm thanh thành công
        this.playSuccessSound();

        console.log(`🎯 Tracking thành công hình ảnh: ${imageName} (ID: ${imageId})`);
    }

    onMarkerLost(imageId) {
        this.showNotification('❌ Mất tracking', 'warning');
        console.log(`❌ Mất tracking hình ảnh ID: ${imageId}`);
    }

    showNotification(message, type = 'info') {
        // Tạo notification tạm thời
        const notification = document.createElement('div');
        notification.style.cssText = `
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: ${type === 'success' ? '#27ae60' : type === 'warning' ? '#f39c12' : '#3498db'};
            color: white;
            padding: 15px 25px;
            border-radius: 25px;
            z-index: 1001;
            font-weight: bold;
            box-shadow: 0 4px 15px rgba(0,0,0,0.3);
            animation: fadeInOut 2s ease-in-out;
        `;
        notification.textContent = message;

        // Thêm CSS animation
        if (!document.getElementById('notification-styles')) {
            const style = document.createElement('style');
            style.id = 'notification-styles';
            style.textContent = `
                @keyframes fadeInOut {
                    0% { opacity: 0; transform: translate(-50%, -50%) scale(0.8); }
                    20% { opacity: 1; transform: translate(-50%, -50%) scale(1); }
                    80% { opacity: 1; transform: translate(-50%, -50%) scale(1); }
                    100% { opacity: 0; transform: translate(-50%, -50%) scale(0.8); }
                }
            `;
            document.head.appendChild(style);
        }

        document.body.appendChild(notification);

        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 2000);
    }

    playSuccessSound() {
        // Tạo âm thanh đơn giản bằng Web Audio API
        try {
            const audioContext = new (window.AudioContext || window.webkitAudioContext)();
            const oscillator = audioContext.createOscillator();
            const gainNode = audioContext.createGain();

            oscillator.connect(gainNode);
            gainNode.connect(audioContext.destination);

            oscillator.frequency.setValueAtTime(800, audioContext.currentTime);
            oscillator.frequency.setValueAtTime(1000, audioContext.currentTime + 0.1);

            gainNode.gain.setValueAtTime(0.3, audioContext.currentTime);
            gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.3);

            oscillator.start(audioContext.currentTime);
            oscillator.stop(audioContext.currentTime + 0.3);
        } catch (error) {
            console.log('Không thể phát âm thanh:', error);
        }
    }

    hideLoading() {
        const loading = document.getElementById('loading');
        loading.classList.add('hidden');
    }

    showError(message) {
        const loading = document.getElementById('loading');
        loading.innerHTML = `
            <div style="color: #ff6b6b; text-align: center;">
                <h3>❌ Lỗi</h3>
                <p>${message}</p>
                <button onclick="location.reload()" style="padding: 10px 20px; margin-top: 20px; background: #3498db; color: white; border: none; border-radius: 5px; cursor: pointer;">
                    Thử lại
                </button>
            </div>
        `;
    }

    setupUI() {
        const toggleButton = document.getElementById('toggleList');
        const imageList = document.getElementById('imageList');
        const instructions = document.getElementById('instructions');
        let isListVisible = true;
        let instructionsVisible = true;

        // Toggle danh sách hình ảnh
        toggleButton.addEventListener('click', () => {
            isListVisible = !isListVisible;
            imageList.style.display = isListVisible ? 'block' : 'none';
            toggleButton.textContent = isListVisible ? '📋' : '👁️';
        });

        // Tự động ẩn instructions sau thời gian khác nhau tùy thiết bị
        const hideDelay = window.innerWidth <= 768 ? 8000 : 12000; // Mobile: 8s, Desktop: 12s
        setTimeout(() => {
            instructionsVisible = false;
            instructions.style.display = 'none';
        }, hideDelay);

        // Double tap để hiện/ẩn instructions (mobile)
        let tapCount = 0;
        let tapTimer = null;

        // Chỉ enable double tap trên mobile
        if ('ontouchstart' in window) {
            document.addEventListener('touchstart', () => {
                tapCount++;

                if (tapTimer) {
                    clearTimeout(tapTimer);
                }

                tapTimer = setTimeout(() => {
                    if (tapCount === 2) {
                        instructionsVisible = !instructionsVisible;
                        instructions.style.display = instructionsVisible ? 'block' : 'none';

                        // Haptic feedback nếu có
                        if (navigator.vibrate) {
                            navigator.vibrate(50);
                        }
                    }
                    tapCount = 0;
                }, 300);
            });
        }

        // Double click trên desktop để toggle instructions
        document.addEventListener('dblclick', (e) => {
            if (e.target.tagName !== 'BUTTON' && !e.target.closest('#imageList') && !e.target.closest('#instructions')) {
                instructionsVisible = !instructionsVisible;
                instructions.style.display = instructionsVisible ? 'block' : 'none';
            }
        });

        // Xử lý orientation change
        window.addEventListener('orientationchange', () => {
            setTimeout(() => {
                this.handleOrientationChange();
            }, 500);
        });

        // Xử lý resize
        window.addEventListener('resize', () => {
            this.handleResize();
        });

        // Xử lý lỗi camera
        navigator.mediaDevices.getUserMedia({ video: true })
            .then(() => {
                console.log('Camera access granted');
            })
            .catch((error) => {
                console.error('Camera access denied:', error);
                this.showError('Không thể truy cập camera. Vui lòng cho phép truy cập camera và thử lại.');
            });
    }

    handleOrientationChange() {
        // Force layout recalculation
        const instructions = document.getElementById('instructions');
        const imageList = document.getElementById('imageList');

        if (instructions) {
            instructions.style.display = 'none';
            setTimeout(() => {
                instructions.style.display = 'block';
            }, 100);
        }

        if (imageList) {
            imageList.style.display = 'none';
            setTimeout(() => {
                imageList.style.display = 'block';
            }, 100);
        }
    }

    handleResize() {
        // Debounce resize events
        clearTimeout(this.resizeTimer);
        this.resizeTimer = setTimeout(() => {
            // Adjust maxImages based on new screen size
            const newMaxImages = window.innerWidth <= 768 ? 6 : 8;
            if (newMaxImages !== this.maxImages) {
                this.maxImages = newMaxImages;
                // Could reload images here if needed
            }
        }, 250);
    }
}

// Khởi tạo ứng dụng khi DOM đã sẵn sàng
document.addEventListener('DOMContentLoaded', () => {
    new ARImageTracker();
});

// Xử lý lỗi toàn cục
window.addEventListener('error', (event) => {
    console.error('Lỗi toàn cục:', event.error);
});

window.addEventListener('unhandledrejection', (event) => {
    console.error('Promise rejection không được xử lý:', event.reason);
});
