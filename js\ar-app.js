class ARImageTracker {
    constructor() {
        this.apiUrl = 'https://picsum.photos/v2/list';
        this.images = [];
        this.scene = null;
        this.assets = null;
        this.currentMarkers = [];
        this.maxImages = 10; // Giới hạn số lượng hình ảnh để tránh quá tải
        
        this.init();
    }
    
    async init() {
        try {
            // Đợi A-Frame khởi tạo xong
            await this.waitForAFrame();
            
            this.scene = document.querySelector('a-scene');
            this.assets = document.querySelector('a-assets');
            
            // Fetch hình ảnh từ API
            await this.fetchImages();
            
            // Tạo image targets
            await this.createImageTargets();
            
            // Ẩn loading screen
            this.hideLoading();
            
            // Setup UI events
            this.setupUI();
            
        } catch (error) {
            console.error('Lỗi khởi tạo AR:', error);
            this.showError('<PERSON><PERSON> lỗi xảy ra khi khởi tạo AR. Vui lòng thử lại.');
        }
    }
    
    waitForAFrame() {
        return new Promise((resolve) => {
            if (document.querySelector('a-scene').hasLoaded) {
                resolve();
            } else {
                document.querySelector('a-scene').addEventListener('loaded', resolve);
            }
        });
    }
    
    async fetchImages() {
        try {
            console.log('Đang fetch hình ảnh từ API...');
            const response = await fetch(this.apiUrl);
            
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            
            const allImages = await response.json();
            
            // Lấy một số hình ảnh ngẫu nhiên
            this.images = this.shuffleArray(allImages).slice(0, this.maxImages);
            
            console.log(`Đã tải ${this.images.length} hình ảnh`);
            
            // Hiển thị danh sách hình ảnh
            this.displayImageList();
            
        } catch (error) {
            console.error('Lỗi khi fetch hình ảnh:', error);
            throw error;
        }
    }
    
    shuffleArray(array) {
        const shuffled = [...array];
        for (let i = shuffled.length - 1; i > 0; i--) {
            const j = Math.floor(Math.random() * (i + 1));
            [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
        }
        return shuffled;
    }
    
    displayImageList() {
        const container = document.getElementById('imageContainer');
        container.innerHTML = '';
        
        this.images.forEach((image, index) => {
            const imageItem = document.createElement('div');
            imageItem.className = 'image-item';
            
            const thumbnailUrl = `https://picsum.photos/id/${image.id}/200/150`;
            
            imageItem.innerHTML = `
                <img src="${thumbnailUrl}" alt="Image ${image.id}" loading="lazy">
                <div class="image-info">
                    <div class="image-author">📷 ${image.author}</div>
                    <div class="image-id">ID: ${image.id} | ${image.width}x${image.height}</div>
                </div>
            `;
            
            container.appendChild(imageItem);
        });
    }
    
    async createImageTargets() {
        console.log('Đang tạo image targets...');
        
        for (let i = 0; i < this.images.length; i++) {
            const image = this.images[i];
            await this.createSingleTarget(image, i);
        }
        
        console.log('Đã tạo xong tất cả image targets');
    }
    
    async createSingleTarget(image, index) {
        const imageId = image.id;
        const imageUrl = `https://picsum.photos/id/${imageId}/400/300`;
        
        // Tạo asset cho hình ảnh
        const imgAsset = document.createElement('img');
        imgAsset.id = `target-${imageId}`;
        imgAsset.src = imageUrl;
        imgAsset.crossOrigin = 'anonymous';
        this.assets.appendChild(imgAsset);
        
        // Đợi hình ảnh load xong
        await new Promise((resolve) => {
            imgAsset.onload = resolve;
            imgAsset.onerror = resolve; // Tiếp tục ngay cả khi có lỗi
        });
        
        // Tạo marker entity
        const marker = document.createElement('a-marker');
        marker.setAttribute('type', 'pattern');
        marker.setAttribute('preset', 'custom');
        marker.setAttribute('url', this.generatePatternUrl(imageUrl));
        marker.setAttribute('id', `marker-${imageId}`);
        
        // Tạo nội dung AR
        const arContent = this.createARContent(image, index);
        marker.appendChild(arContent);
        
        // Thêm event listeners
        marker.addEventListener('markerFound', () => {
            console.log(`Đã tìm thấy marker: ${imageId}`);
            this.onMarkerFound(imageId);
        });
        
        marker.addEventListener('markerLost', () => {
            console.log(`Mất marker: ${imageId}`);
            this.onMarkerLost(imageId);
        });
        
        this.scene.appendChild(marker);
        this.currentMarkers.push(marker);
    }
    
    generatePatternUrl(imageUrl) {
        // Trong thực tế, bạn cần tạo pattern file (.patt) từ hình ảnh
        // Ở đây tôi sử dụng một pattern mặc định
        return 'https://raw.githubusercontent.com/AR-js-org/AR.js/master/data/data/patt.hiro';
    }
    
    createARContent(image, index) {
        const group = document.createElement('a-entity');
        
        // Tạo một hộp 3D với thông tin hình ảnh
        const box = document.createElement('a-box');
        box.setAttribute('position', '0 0.5 0');
        box.setAttribute('material', `color: ${this.getRandomColor()}`);
        box.setAttribute('animation', 'property: rotation; to: 0 360 0; loop: true; dur: 10000');
        
        // Tạo text hiển thị thông tin
        const text = document.createElement('a-text');
        text.setAttribute('value', `${image.author}\nID: ${image.id}`);
        text.setAttribute('position', '0 1.5 0');
        text.setAttribute('align', 'center');
        text.setAttribute('color', 'white');
        text.setAttribute('scale', '2 2 2');
        
        // Tạo hình ảnh 3D
        const plane = document.createElement('a-plane');
        plane.setAttribute('position', '0 0 0.1');
        plane.setAttribute('width', '2');
        plane.setAttribute('height', '1.5');
        plane.setAttribute('material', `src: #target-${image.id}; transparent: true`);
        
        group.appendChild(box);
        group.appendChild(text);
        group.appendChild(plane);
        
        return group;
    }
    
    getRandomColor() {
        const colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7', '#DDA0DD', '#98D8C8'];
        return colors[Math.floor(Math.random() * colors.length)];
    }
    
    onMarkerFound(imageId) {
        // Có thể thêm hiệu ứng hoặc âm thanh khi tìm thấy marker
        console.log(`🎯 Tracking thành công hình ảnh ID: ${imageId}`);
    }
    
    onMarkerLost(imageId) {
        console.log(`❌ Mất tracking hình ảnh ID: ${imageId}`);
    }
    
    hideLoading() {
        const loading = document.getElementById('loading');
        loading.classList.add('hidden');
    }
    
    showError(message) {
        const loading = document.getElementById('loading');
        loading.innerHTML = `
            <div style="color: #ff6b6b; text-align: center;">
                <h3>❌ Lỗi</h3>
                <p>${message}</p>
                <button onclick="location.reload()" style="padding: 10px 20px; margin-top: 20px; background: #3498db; color: white; border: none; border-radius: 5px; cursor: pointer;">
                    Thử lại
                </button>
            </div>
        `;
    }
    
    setupUI() {
        const toggleButton = document.getElementById('toggleList');
        const imageList = document.getElementById('imageList');
        let isListVisible = true;
        
        toggleButton.addEventListener('click', () => {
            isListVisible = !isListVisible;
            imageList.style.display = isListVisible ? 'block' : 'none';
            toggleButton.textContent = isListVisible ? '📋' : '👁️';
        });
    }
}

// Khởi tạo ứng dụng khi DOM đã sẵn sàng
document.addEventListener('DOMContentLoaded', () => {
    new ARImageTracker();
});

// Xử lý lỗi toàn cục
window.addEventListener('error', (event) => {
    console.error('Lỗi toàn cục:', event.error);
});

window.addEventListener('unhandledrejection', (event) => {
    console.error('Promise rejection không được xử lý:', event.reason);
});
