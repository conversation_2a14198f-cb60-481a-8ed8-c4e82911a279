<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Simple Camera Test</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            background: #f0f0f0;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            text-align: center;
        }
        
        #video {
            width: 100%;
            max-width: 640px;
            height: auto;
            border: 2px solid #333;
            border-radius: 10px;
            background: #000;
        }
        
        #status {
            margin: 20px 0;
            padding: 15px;
            border-radius: 10px;
            font-weight: bold;
        }
        
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .warning { background: #fff3cd; color: #856404; }
        .info { background: #d1ecf1; color: #0c5460; }
        
        button {
            padding: 10px 20px;
            margin: 10px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
        }
        
        .btn-primary { background: #007bff; color: white; }
        .btn-danger { background: #dc3545; color: white; }
        
        #info-panel {
            text-align: left;
            background: white;
            padding: 20px;
            border-radius: 10px;
            margin-top: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>📷 Test Camera Đơn Giản</h1>
        
        <div id="status" class="info">
            Nhấn "Bật Camera" để bắt đầu test
        </div>
        
        <video id="video" autoplay muted playsinline></video>
        
        <div>
            <button id="startBtn" class="btn-primary">📷 Bật Camera</button>
            <button id="stopBtn" class="btn-danger" disabled>⏹️ Tắt Camera</button>
        </div>
        
        <div id="info-panel">
            <h3>ℹ️ Thông tin hệ thống:</h3>
            <div id="system-info">Đang kiểm tra...</div>
            
            <h3>🔧 Nếu camera không hoạt động:</h3>
            <ul>
                <li><strong>Trên Windows:</strong> Kiểm tra Privacy Settings → Camera → Allow apps to access camera</li>
                <li><strong>Trên Browser:</strong> Click vào icon 🔒 bên trái URL và cho phép Camera</li>
                <li><strong>HTTPS:</strong> Sử dụng localhost hoặc HTTPS (không phải file://)</li>
                <li><strong>Antivirus:</strong> Tạm thời tắt antivirus nếu chặn camera</li>
            </ul>
            
            <h3>🌐 Cách chạy localhost:</h3>
            <pre style="background: #f8f9fa; padding: 10px; border-radius: 5px;">
# Sử dụng Python
cd d:\VRTech\BEProcessCaptured
python -m http.server 8000

# Hoặc Node.js
npx http-server -p 8000

# Sau đó mở: http://localhost:8000/simple-camera-test.html
            </pre>
        </div>
    </div>

    <script>
        const video = document.getElementById('video');
        const startBtn = document.getElementById('startBtn');
        const stopBtn = document.getElementById('stopBtn');
        const status = document.getElementById('status');
        const systemInfo = document.getElementById('system-info');
        
        let currentStream = null;
        
        // Hiển thị thông tin hệ thống
        function displaySystemInfo() {
            const info = {
                'User Agent': navigator.userAgent,
                'Platform': navigator.platform,
                'Language': navigator.language,
                'Online': navigator.onLine,
                'Cookies Enabled': navigator.cookieEnabled,
                'Protocol': window.location.protocol,
                'Host': window.location.host || 'File System',
                'MediaDevices Support': !!navigator.mediaDevices,
                'getUserMedia Support': !!(navigator.mediaDevices && navigator.mediaDevices.getUserMedia)
            };
            
            let html = '<table style="width: 100%; border-collapse: collapse;">';
            for (const [key, value] of Object.entries(info)) {
                const valueColor = key.includes('Support') ? (value ? 'green' : 'red') : 'black';
                html += `
                    <tr style="border-bottom: 1px solid #ddd;">
                        <td style="padding: 5px; font-weight: bold;">${key}:</td>
                        <td style="padding: 5px; color: ${valueColor};">${value}</td>
                    </tr>
                `;
            }
            html += '</table>';
            systemInfo.innerHTML = html;
        }
        
        // Bật camera
        async function startCamera() {
            try {
                status.className = 'info';
                status.textContent = '📷 Đang khởi động camera...';
                
                // Yêu cầu quyền truy cập camera
                const constraints = {
                    video: {
                        width: { ideal: 1280 },
                        height: { ideal: 720 },
                        facingMode: 'environment' // Ưu tiên camera sau (mobile)
                    },
                    audio: false
                };
                
                currentStream = await navigator.mediaDevices.getUserMedia(constraints);
                video.srcObject = currentStream;
                
                status.className = 'success';
                status.textContent = '✅ Camera hoạt động tốt! Bạn có thể thấy hình ảnh từ camera.';
                
                startBtn.disabled = true;
                stopBtn.disabled = false;
                
                // Log thông tin stream
                const videoTrack = currentStream.getVideoTracks()[0];
                const settings = videoTrack.getSettings();
                console.log('Camera settings:', settings);
                
                // Hiển thị thông tin camera
                setTimeout(() => {
                    status.innerHTML = `
                        ✅ Camera hoạt động tốt!<br>
                        📐 Resolution: ${settings.width}x${settings.height}<br>
                        📷 Device: ${videoTrack.label || 'Unknown Camera'}
                    `;
                }, 1000);
                
            } catch (error) {
                console.error('Camera error:', error);
                
                status.className = 'error';
                
                let errorMessage = '❌ Không thể truy cập camera: ';
                switch (error.name) {
                    case 'NotAllowedError':
                        errorMessage += 'Bạn đã từ chối quyền truy cập camera. Hãy cho phép và thử lại.';
                        break;
                    case 'NotFoundError':
                        errorMessage += 'Không tìm thấy camera nào trên thiết bị.';
                        break;
                    case 'NotReadableError':
                        errorMessage += 'Camera đang được sử dụng bởi ứng dụng khác.';
                        break;
                    case 'OverconstrainedError':
                        errorMessage += 'Camera không hỗ trợ độ phân giải yêu cầu.';
                        break;
                    case 'SecurityError':
                        errorMessage += 'Lỗi bảo mật. Hãy sử dụng HTTPS hoặc localhost.';
                        break;
                    default:
                        errorMessage += error.message;
                }
                
                status.textContent = errorMessage;
            }
        }
        
        // Tắt camera
        function stopCamera() {
            if (currentStream) {
                currentStream.getTracks().forEach(track => track.stop());
                currentStream = null;
                video.srcObject = null;
                
                status.className = 'warning';
                status.textContent = '⏹️ Camera đã được tắt.';
                
                startBtn.disabled = false;
                stopBtn.disabled = true;
            }
        }
        
        // Event listeners
        startBtn.addEventListener('click', startCamera);
        stopBtn.addEventListener('click', stopCamera);
        
        // Khởi tạo
        window.addEventListener('load', () => {
            displaySystemInfo();
            
            // Kiểm tra hỗ trợ
            if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
                status.className = 'error';
                status.textContent = '❌ Browser không hỗ trợ camera API. Hãy sử dụng Chrome, Firefox, hoặc Safari mới nhất.';
                startBtn.disabled = true;
            }
        });
        
        // Cleanup khi đóng trang
        window.addEventListener('beforeunload', stopCamera);
    </script>
</body>
</html>
