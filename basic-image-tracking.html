<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no, viewport-fit=cover">
    <title>Basic Image Tracking</title>
    <style>
        body {
            margin: 0;
            font-family: Arial, sans-serif;
            background: #000;
            overflow: hidden;
        }
        
        #video {
            width: 100vw;
            height: 100vh;
            object-fit: cover;
        }
        
        #overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: 10;
        }
        
        #instructions {
            position: fixed;
            top: 20px;
            left: 20px;
            right: 20px;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 15px;
            border-radius: 10px;
            z-index: 100;
            font-size: 14px;
            pointer-events: auto;
        }
        
        #instructions h3 {
            margin: 0 0 10px 0;
            color: #4ECDC4;
        }
        
        #imageList {
            position: fixed;
            bottom: 20px;
            left: 20px;
            right: 20px;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 15px;
            border-radius: 10px;
            z-index: 100;
            max-height: 150px;
            overflow-y: auto;
            pointer-events: auto;
        }
        
        .image-item {
            display: flex;
            align-items: center;
            padding: 8px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-bottom: 6px;
        }
        
        .image-item:hover {
            background: rgba(255, 255, 255, 0.2);
        }
        
        .image-item img {
            width: 50px;
            height: 38px;
            object-fit: cover;
            border-radius: 4px;
            margin-right: 10px;
        }
        
        .image-info {
            flex: 1;
            font-size: 12px;
        }
        
        .image-author {
            font-weight: bold;
            color: #ffffff;
            margin-bottom: 2px;
        }
        
        .image-id {
            color: #cccccc;
            font-size: 11px;
        }
        
        #arContent {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(0, 0, 0, 0.9);
            color: white;
            padding: 20px;
            border-radius: 15px;
            text-align: center;
            display: none;
            z-index: 1000;
            border: 3px solid #4ECDC4;
            box-shadow: 0 0 20px rgba(78, 205, 196, 0.5);
        }
        
        #arContent.show {
            display: block;
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0% { transform: translate(-50%, -50%) scale(1); }
            50% { transform: translate(-50%, -50%) scale(1.05); }
            100% { transform: translate(-50%, -50%) scale(1); }
        }
        
        #status {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(0, 0, 0, 0.9);
            color: white;
            padding: 15px 25px;
            border-radius: 25px;
            z-index: 1001;
            font-weight: bold;
            display: none;
        }
        
        .success { color: #4CAF50; }
        .error { color: #f44336; }
        .warning { color: #ff9800; }
        
        #loading {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            z-index: 2000;
        }
        
        #loading.hidden { display: none; }
        
        .spinner {
            border: 4px solid rgba(255, 255, 255, 0.3);
            border-top: 4px solid #ffffff;
            border-radius: 50%;
            width: 60px;
            height: 60px;
            animation: spin 1s linear infinite;
            margin-bottom: 30px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        #toggleInstructions {
            position: fixed;
            top: 20px;
            right: 20px;
            background: #4ECDC4;
            color: white;
            border: none;
            padding: 10px;
            border-radius: 50%;
            cursor: pointer;
            z-index: 101;
            font-size: 16px;
            width: 40px;
            height: 40px;
        }
    </style>
</head>
<body>
    <div id="loading">
        <div class="spinner"></div>
        <p>Đang khởi tạo camera...</p>
        <p>Vui lòng cho phép truy cập camera</p>
    </div>
    
    <video id="video" autoplay muted playsinline></video>
    <canvas id="overlay"></canvas>
    
    <button id="toggleInstructions">?</button>
    
    <div id="instructions">
        <h3>🎯 Basic Image Tracking</h3>
        <p>1. Chọn hình ảnh từ danh sách bên dưới</p>
        <p>2. Click để mở full-size và in ra</p>
        <p>3. Giữ hình ảnh trước camera</p>
        <p>4. Hệ thống sẽ detect màu sắc tương tự</p>
    </div>
    
    <div id="imageList">
        <h4>📸 Hình ảnh tracking:</h4>
        <div id="imageContainer">Đang tải...</div>
    </div>
    
    <div id="arContent">
        <h2 id="arTitle">🎯 AR Content</h2>
        <p id="arAuthor">Tác giả</p>
        <p id="arId">ID</p>
        <div style="margin-top: 15px;">
            <div style="display: inline-block; width: 20px; height: 20px; background: #FF6B6B; border-radius: 50%; margin: 5px; animation: bounce 1s infinite;"></div>
            <div style="display: inline-block; width: 20px; height: 20px; background: #4ECDC4; border-radius: 50%; margin: 5px; animation: bounce 1s infinite 0.2s;"></div>
            <div style="display: inline-block; width: 20px; height: 20px; background: #96CEB4; border-radius: 50%; margin: 5px; animation: bounce 1s infinite 0.4s;"></div>
        </div>
    </div>
    
    <div id="status">
        <span id="status-text"></span>
    </div>

    <script>
        class BasicImageTracker {
            constructor() {
                this.apiUrl = 'https://picsum.photos/v2/list';
                this.images = [];
                this.video = null;
                this.canvas = null;
                this.ctx = null;
                this.isTracking = false;
                this.targetColors = [];
                this.maxImages = 5;
                this.currentMatch = null;
                this.matchThreshold = 0.8;
                
                this.init();
            }
            
            async init() {
                try {
                    this.video = document.getElementById('video');
                    this.canvas = document.getElementById('overlay');
                    this.ctx = this.canvas.getContext('2d');
                    
                    // Setup canvas size
                    this.resizeCanvas();
                    window.addEventListener('resize', () => this.resizeCanvas());
                    
                    // Setup camera
                    await this.setupCamera();
                    
                    // Fetch images
                    await this.fetchImages();
                    
                    // Start tracking
                    this.startTracking();
                    
                    // Setup UI
                    this.setupUI();
                    
                    this.hideLoading();
                    
                } catch (error) {
                    console.error('Lỗi khởi tạo:', error);
                    this.showError(`Lỗi: ${error.message}`);
                }
            }
            
            resizeCanvas() {
                this.canvas.width = window.innerWidth;
                this.canvas.height = window.innerHeight;
            }
            
            async setupCamera() {
                try {
                    console.log('Đang setup camera...');
                    
                    const constraints = {
                        video: { 
                            width: { ideal: 640 },
                            height: { ideal: 480 },
                            facingMode: 'environment'
                        }
                    };
                    
                    const stream = await navigator.mediaDevices.getUserMedia(constraints);
                    this.video.srcObject = stream;
                    
                    return new Promise((resolve, reject) => {
                        this.video.onloadedmetadata = () => {
                            console.log('✅ Camera loaded successfully');
                            this.video.play();
                            resolve();
                        };
                        
                        this.video.onerror = (error) => {
                            console.error('❌ Video error:', error);
                            reject(new Error('Không thể khởi động video'));
                        };
                        
                        setTimeout(() => {
                            if (this.video.readyState === 0) {
                                reject(new Error('Camera timeout - Vui lòng cho phép truy cập camera'));
                            }
                        }, 10000);
                    });
                    
                } catch (error) {
                    console.error('❌ Camera setup error:', error);
                    throw new Error(`Không thể truy cập camera: ${error.message}`);
                }
            }
            
            async fetchImages() {
                try {
                    const response = await fetch(this.apiUrl);
                    const allImages = await response.json();
                    
                    this.images = allImages.slice(0, this.maxImages);
                    
                    // Extract dominant colors for each image
                    for (const image of this.images) {
                        const color = await this.extractDominantColor(image);
                        this.targetColors.push({
                            id: image.id,
                            author: image.author,
                            color: color
                        });
                    }
                    
                    this.displayImageList();
                    console.log('✅ Images loaded and processed');
                    
                } catch (error) {
                    console.error('❌ Error fetching images:', error);
                    throw error;
                }
            }
            
            async extractDominantColor(image) {
                return new Promise((resolve) => {
                    const img = new Image();
                    img.crossOrigin = 'anonymous';
                    img.onload = () => {
                        const canvas = document.createElement('canvas');
                        const ctx = canvas.getContext('2d');
                        canvas.width = 50;
                        canvas.height = 50;
                        
                        ctx.drawImage(img, 0, 0, 50, 50);
                        const imageData = ctx.getImageData(0, 0, 50, 50);
                        const data = imageData.data;
                        
                        let r = 0, g = 0, b = 0;
                        for (let i = 0; i < data.length; i += 4) {
                            r += data[i];
                            g += data[i + 1];
                            b += data[i + 2];
                        }
                        
                        const pixelCount = data.length / 4;
                        resolve({
                            r: Math.round(r / pixelCount),
                            g: Math.round(g / pixelCount),
                            b: Math.round(b / pixelCount)
                        });
                    };
                    img.onerror = () => resolve({ r: 128, g: 128, b: 128 });
                    img.src = `https://picsum.photos/id/${image.id}/100/100`;
                });
            }
            
            displayImageList() {
                const container = document.getElementById('imageContainer');
                container.innerHTML = '';
                
                this.images.forEach((image, index) => {
                    const imageItem = document.createElement('div');
                    imageItem.className = 'image-item';
                    
                    const thumbnailUrl = `https://picsum.photos/id/${image.id}/200/150`;
                    const color = this.targetColors[index];
                    
                    imageItem.innerHTML = `
                        <img src="${thumbnailUrl}" alt="Image ${image.id}">
                        <div class="image-info">
                            <div class="image-author">📷 ${image.author}</div>
                            <div class="image-id">ID: ${image.id}</div>
                            <div style="font-size: 10px; color: #4ECDC4;">
                                RGB(${color.color.r}, ${color.color.g}, ${color.color.b})
                            </div>
                        </div>
                    `;
                    
                    imageItem.addEventListener('click', () => {
                        const fullUrl = `https://picsum.photos/id/${image.id}/800/600`;
                        window.open(fullUrl, '_blank');
                    });
                    
                    container.appendChild(imageItem);
                });
            }
            
            startTracking() {
                this.isTracking = true;
                this.trackingLoop();
                console.log('🎯 Tracking started');
            }
            
            trackingLoop() {
                if (!this.isTracking) return;
                
                try {
                    if (this.video.readyState >= 2) {
                        // Sample colors from center of video
                        const centerColor = this.sampleCenterColor();
                        
                        // Find best match
                        const match = this.findColorMatch(centerColor);
                        
                        if (match && match.similarity > this.matchThreshold) {
                            this.showARContent(match);
                        } else {
                            this.hideARContent();
                        }
                    }
                } catch (error) {
                    console.error('Tracking error:', error);
                }
                
                requestAnimationFrame(() => this.trackingLoop());
            }
            
            sampleCenterColor() {
                const canvas = document.createElement('canvas');
                const ctx = canvas.getContext('2d');
                canvas.width = this.video.videoWidth || 640;
                canvas.height = this.video.videoHeight || 480;
                
                ctx.drawImage(this.video, 0, 0, canvas.width, canvas.height);
                
                // Sample from center area
                const centerX = canvas.width / 2;
                const centerY = canvas.height / 2;
                const sampleSize = 50;
                
                const imageData = ctx.getImageData(
                    centerX - sampleSize/2, 
                    centerY - sampleSize/2, 
                    sampleSize, 
                    sampleSize
                );
                
                const data = imageData.data;
                let r = 0, g = 0, b = 0;
                
                for (let i = 0; i < data.length; i += 4) {
                    r += data[i];
                    g += data[i + 1];
                    b += data[i + 2];
                }
                
                const pixelCount = data.length / 4;
                return {
                    r: Math.round(r / pixelCount),
                    g: Math.round(g / pixelCount),
                    b: Math.round(b / pixelCount)
                };
            }
            
            findColorMatch(currentColor) {
                let bestMatch = null;
                let bestSimilarity = 0;
                
                for (const target of this.targetColors) {
                    const similarity = this.calculateColorSimilarity(currentColor, target.color);
                    
                    if (similarity > bestSimilarity) {
                        bestSimilarity = similarity;
                        bestMatch = {
                            ...target,
                            similarity: similarity
                        };
                    }
                }
                
                return bestMatch;
            }
            
            calculateColorSimilarity(color1, color2) {
                const rDiff = Math.abs(color1.r - color2.r);
                const gDiff = Math.abs(color1.g - color2.g);
                const bDiff = Math.abs(color1.b - color2.b);
                
                const totalDiff = rDiff + gDiff + bDiff;
                const maxDiff = 255 * 3;
                
                return 1 - (totalDiff / maxDiff);
            }
            
            showARContent(match) {
                if (!this.currentMatch || this.currentMatch.id !== match.id) {
                    const image = this.images.find(img => img.id == match.id);
                    
                    document.getElementById('arTitle').textContent = '🎯 Image Detected!';
                    document.getElementById('arAuthor').textContent = `📷 ${match.author}`;
                    document.getElementById('arId').textContent = `ID: ${match.id} | Similarity: ${Math.round(match.similarity * 100)}%`;
                    
                    document.getElementById('arContent').classList.add('show');
                    
                    this.showStatus(`🎯 Tracking: ${match.author}`, 'success');
                    this.currentMatch = match;
                }
            }
            
            hideARContent() {
                document.getElementById('arContent').classList.remove('show');
                this.currentMatch = null;
            }
            
            showStatus(message, type) {
                const status = document.getElementById('status');
                const statusText = document.getElementById('status-text');
                
                statusText.innerHTML = `<span class="${type}">${message}</span>`;
                status.style.display = 'block';
                
                setTimeout(() => {
                    status.style.display = 'none';
                }, 1500);
            }
            
            showError(message) {
                const loading = document.getElementById('loading');
                loading.innerHTML = `
                    <div style="color: #ff6b6b; text-align: center;">
                        <h3>❌ Lỗi</h3>
                        <p>${message}</p>
                        <button onclick="location.reload()" style="padding: 10px 20px; margin-top: 20px; background: #3498db; color: white; border: none; border-radius: 5px; cursor: pointer;">
                            Thử lại
                        </button>
                    </div>
                `;
            }
            
            hideLoading() {
                document.getElementById('loading').classList.add('hidden');
            }
            
            setupUI() {
                const toggleBtn = document.getElementById('toggleInstructions');
                const instructions = document.getElementById('instructions');
                let instructionsVisible = true;
                
                toggleBtn.addEventListener('click', () => {
                    instructionsVisible = !instructionsVisible;
                    instructions.style.display = instructionsVisible ? 'block' : 'none';
                    toggleBtn.textContent = instructionsVisible ? '?' : '👁️';
                });
                
                // Auto hide instructions after 10 seconds
                setTimeout(() => {
                    instructions.style.display = 'none';
                    instructionsVisible = false;
                    toggleBtn.textContent = '👁️';
                }, 10000);
            }
        }
        
        // Add bounce animation
        const style = document.createElement('style');
        style.textContent = `
            @keyframes bounce {
                0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
                40% { transform: translateY(-10px); }
                60% { transform: translateY(-5px); }
            }
        `;
        document.head.appendChild(style);
        
        // Initialize when DOM ready
        document.addEventListener('DOMContentLoaded', () => {
            new BasicImageTracker();
        });
    </script>
</body>
</html>
