<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no, viewport-fit=cover">
    <title>Simple Image Tracking</title>
    
    <!-- OpenCV.js for computer vision -->
    <script async src="https://docs.opencv.org/4.8.0/opencv.js"></script>
    <script src="https://aframe.io/releases/1.4.0/aframe.min.js"></script>
    
    <style>
        body {
            margin: 0;
            font-family: Arial, sans-serif;
            background: #000;
            overflow: hidden;
        }
        
        #loading {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            z-index: 1000;
        }
        
        #loading.hidden { display: none; }
        
        .spinner {
            border: 4px solid rgba(255, 255, 255, 0.3);
            border-top: 4px solid #ffffff;
            border-radius: 50%;
            width: 60px;
            height: 60px;
            animation: spin 1s linear infinite;
            margin-bottom: 30px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        #instructions {
            position: fixed;
            top: 20px;
            left: 20px;
            right: 20px;
            background: rgba(0, 0, 0, 0.9);
            color: white;
            padding: 15px;
            border-radius: 12px;
            z-index: 100;
            font-size: 14px;
        }
        
        #instructions h3 {
            margin: 0 0 10px 0;
            color: #4ECDC4;
        }
        
        #imageList {
            position: fixed;
            bottom: 20px;
            left: 20px;
            right: 20px;
            background: rgba(0, 0, 0, 0.9);
            color: white;
            padding: 15px;
            border-radius: 12px;
            z-index: 100;
            max-height: 180px;
            overflow-y: auto;
        }
        
        .image-item {
            display: flex;
            align-items: center;
            padding: 8px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-bottom: 6px;
        }
        
        .image-item:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: translateY(-1px);
        }
        
        .image-item img {
            width: 50px;
            height: 38px;
            object-fit: cover;
            border-radius: 4px;
            margin-right: 10px;
        }
        
        .image-info {
            flex: 1;
            font-size: 12px;
        }
        
        .image-author {
            font-weight: bold;
            color: #ffffff;
            margin-bottom: 2px;
        }
        
        .image-id {
            color: #cccccc;
            font-size: 11px;
        }
        
        #videoCanvas {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 1;
        }
        
        #arCanvas {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 2;
            pointer-events: none;
        }
        
        #status {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(0, 0, 0, 0.9);
            color: white;
            padding: 15px 25px;
            border-radius: 25px;
            z-index: 1001;
            font-weight: bold;
            display: none;
        }
        
        .success { color: #4CAF50; }
        .error { color: #f44336; }
        .warning { color: #ff9800; }
    </style>
</head>
<body>
    <div id="loading">
        <div class="spinner"></div>
        <p>Đang tải OpenCV.js...</p>
        <p>Đang chuẩn bị Image Tracking...</p>
    </div>
    
    <div id="instructions">
        <h3>🎯 Computer Vision Image Tracking</h3>
        <p>1. Chọn hình ảnh từ danh sách</p>
        <p>2. Click để mở và in ra</p>
        <p>3. Hướng camera vào hình ảnh</p>
        <p>4. Hệ thống sẽ tự động detect!</p>
    </div>
    
    <div id="imageList">
        <h4>📸 Hình ảnh tracking:</h4>
        <div id="imageContainer">Đang tải...</div>
    </div>
    
    <div id="status">
        <span id="status-text"></span>
    </div>
    
    <video id="video" autoplay muted playsinline style="display: none;"></video>
    <canvas id="videoCanvas"></canvas>
    <canvas id="arCanvas"></canvas>

    <script>
        class SimpleImageTracker {
            constructor() {
                this.apiUrl = 'https://picsum.photos/v2/list';
                this.images = [];
                this.video = null;
                this.videoCanvas = null;
                this.arCanvas = null;
                this.videoCtx = null;
                this.arCtx = null;
                this.isTracking = false;
                this.targetImages = [];
                this.maxImages = 4;
                
                this.init();
            }
            
            async init() {
                try {
                    // Đợi OpenCV load
                    await this.waitForOpenCV();
                    
                    // Setup canvas và video
                    this.setupCanvas();
                    
                    // Fetch hình ảnh
                    await this.fetchImages();
                    
                    // Setup camera
                    await this.setupCamera();
                    
                    // Bắt đầu tracking
                    this.startTracking();
                    
                    this.hideLoading();
                    
                } catch (error) {
                    console.error('Lỗi khởi tạo:', error);
                    this.showError('Có lỗi xảy ra. Vui lòng thử lại.');
                }
            }
            
            waitForOpenCV() {
                return new Promise((resolve) => {
                    if (typeof cv !== 'undefined') {
                        cv.onRuntimeInitialized = resolve;
                    } else {
                        const checkCV = setInterval(() => {
                            if (typeof cv !== 'undefined') {
                                clearInterval(checkCV);
                                cv.onRuntimeInitialized = resolve;
                            }
                        }, 100);
                    }
                });
            }
            
            setupCanvas() {
                this.video = document.getElementById('video');
                this.videoCanvas = document.getElementById('videoCanvas');
                this.arCanvas = document.getElementById('arCanvas');
                this.videoCtx = this.videoCanvas.getContext('2d');
                this.arCtx = this.arCanvas.getContext('2d');
                
                // Resize canvas
                const resize = () => {
                    this.videoCanvas.width = window.innerWidth;
                    this.videoCanvas.height = window.innerHeight;
                    this.arCanvas.width = window.innerWidth;
                    this.arCanvas.height = window.innerHeight;
                };
                
                resize();
                window.addEventListener('resize', resize);
            }
            
            async fetchImages() {
                const response = await fetch(this.apiUrl);
                const allImages = await response.json();
                
                this.images = allImages.slice(0, this.maxImages);
                
                // Load target images
                for (const image of this.images) {
                    await this.loadTargetImage(image);
                }
                
                this.displayImageList();
            }
            
            async loadTargetImage(image) {
                return new Promise((resolve) => {
                    const img = new Image();
                    img.crossOrigin = 'anonymous';
                    img.onload = () => {
                        this.targetImages.push({
                            id: image.id,
                            author: image.author,
                            element: img,
                            features: this.extractFeatures(img)
                        });
                        resolve();
                    };
                    img.onerror = resolve;
                    img.src = `https://picsum.photos/id/${image.id}/400/300`;
                });
            }
            
            extractFeatures(img) {
                // Simplified feature extraction
                const canvas = document.createElement('canvas');
                const ctx = canvas.getContext('2d');
                canvas.width = img.width;
                canvas.height = img.height;
                
                ctx.drawImage(img, 0, 0);
                const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
                
                // Extract basic features (simplified)
                const features = {
                    width: img.width,
                    height: img.height,
                    avgColor: this.getAverageColor(imageData),
                    edges: this.detectEdges(imageData)
                };
                
                return features;
            }
            
            getAverageColor(imageData) {
                const data = imageData.data;
                let r = 0, g = 0, b = 0;
                
                for (let i = 0; i < data.length; i += 4) {
                    r += data[i];
                    g += data[i + 1];
                    b += data[i + 2];
                }
                
                const pixelCount = data.length / 4;
                return {
                    r: Math.round(r / pixelCount),
                    g: Math.round(g / pixelCount),
                    b: Math.round(b / pixelCount)
                };
            }
            
            detectEdges(imageData) {
                // Simplified edge detection
                const data = imageData.data;
                const width = imageData.width;
                const height = imageData.height;
                let edgeCount = 0;
                
                for (let y = 1; y < height - 1; y++) {
                    for (let x = 1; x < width - 1; x++) {
                        const idx = (y * width + x) * 4;
                        const current = data[idx];
                        const right = data[idx + 4];
                        const bottom = data[(y + 1) * width * 4 + x * 4];
                        
                        if (Math.abs(current - right) > 30 || Math.abs(current - bottom) > 30) {
                            edgeCount++;
                        }
                    }
                }
                
                return edgeCount;
            }
            
            displayImageList() {
                const container = document.getElementById('imageContainer');
                container.innerHTML = '';
                
                this.images.forEach((image, index) => {
                    const imageItem = document.createElement('div');
                    imageItem.className = 'image-item';
                    
                    const thumbnailUrl = `https://picsum.photos/id/${image.id}/200/150`;
                    
                    imageItem.innerHTML = `
                        <img src="${thumbnailUrl}" alt="Image ${image.id}">
                        <div class="image-info">
                            <div class="image-author">📷 ${image.author}</div>
                            <div class="image-id">ID: ${image.id}</div>
                        </div>
                    `;
                    
                    imageItem.addEventListener('click', () => {
                        const fullUrl = `https://picsum.photos/id/${image.id}/800/600`;
                        window.open(fullUrl, '_blank');
                    });
                    
                    container.appendChild(imageItem);
                });
            }
            
            async setupCamera() {
                const stream = await navigator.mediaDevices.getUserMedia({
                    video: { 
                        width: { ideal: 1280 },
                        height: { ideal: 720 },
                        facingMode: 'environment'
                    }
                });
                
                this.video.srcObject = stream;
                await new Promise(resolve => {
                    this.video.onloadedmetadata = resolve;
                });
            }
            
            startTracking() {
                this.isTracking = true;
                this.trackingLoop();
            }
            
            trackingLoop() {
                if (!this.isTracking) return;
                
                // Draw video frame
                this.videoCtx.drawImage(this.video, 0, 0, this.videoCanvas.width, this.videoCanvas.height);
                
                // Get current frame
                const frameData = this.videoCtx.getImageData(0, 0, this.videoCanvas.width, this.videoCanvas.height);
                
                // Try to match with target images
                const match = this.findImageMatch(frameData);
                
                if (match) {
                    this.drawARContent(match);
                    this.showStatus(`🎯 Tracking: ${match.author}`, 'success');
                } else {
                    // Clear AR canvas
                    this.arCtx.clearRect(0, 0, this.arCanvas.width, this.arCanvas.height);
                }
                
                requestAnimationFrame(() => this.trackingLoop());
            }
            
            findImageMatch(frameData) {
                // Simplified matching algorithm
                const frameFeatures = {
                    avgColor: this.getAverageColor(frameData),
                    edges: this.detectEdges(frameData)
                };
                
                let bestMatch = null;
                let bestScore = 0;
                
                for (const target of this.targetImages) {
                    const score = this.calculateSimilarity(frameFeatures, target.features);
                    
                    if (score > 0.7 && score > bestScore) { // Threshold
                        bestScore = score;
                        bestMatch = target;
                    }
                }
                
                return bestMatch;
            }
            
            calculateSimilarity(features1, features2) {
                // Color similarity
                const colorDiff = Math.abs(features1.avgColor.r - features2.avgColor.r) +
                                Math.abs(features1.avgColor.g - features2.avgColor.g) +
                                Math.abs(features1.avgColor.b - features2.avgColor.b);
                
                const colorSimilarity = Math.max(0, 1 - colorDiff / (255 * 3));
                
                // Edge similarity
                const edgeDiff = Math.abs(features1.edges - features2.edges);
                const edgeSimilarity = Math.max(0, 1 - edgeDiff / Math.max(features1.edges, features2.edges, 1));
                
                return (colorSimilarity + edgeSimilarity) / 2;
            }
            
            drawARContent(match) {
                const ctx = this.arCtx;
                const centerX = this.arCanvas.width / 2;
                const centerY = this.arCanvas.height / 2;
                
                // Clear previous content
                ctx.clearRect(0, 0, this.arCanvas.width, this.arCanvas.height);
                
                // Draw AR elements
                ctx.save();
                
                // Draw border
                ctx.strokeStyle = '#4ECDC4';
                ctx.lineWidth = 4;
                ctx.strokeRect(centerX - 150, centerY - 100, 300, 200);
                
                // Draw text
                ctx.fillStyle = 'white';
                ctx.font = 'bold 24px Arial';
                ctx.textAlign = 'center';
                ctx.fillText(`📷 ${match.author}`, centerX, centerY - 120);
                
                ctx.fillStyle = '#4ECDC4';
                ctx.font = '18px Arial';
                ctx.fillText(`ID: ${match.id}`, centerX, centerY + 130);
                
                // Draw animated elements
                const time = Date.now() * 0.005;
                const radius = 20 + Math.sin(time) * 5;
                
                ctx.fillStyle = '#FF6B6B';
                ctx.beginPath();
                ctx.arc(centerX - 100, centerY, radius, 0, Math.PI * 2);
                ctx.fill();
                
                ctx.fillStyle = '#96CEB4';
                ctx.beginPath();
                ctx.arc(centerX + 100, centerY, radius, 0, Math.PI * 2);
                ctx.fill();
                
                ctx.restore();
            }
            
            showStatus(message, type) {
                const status = document.getElementById('status');
                const statusText = document.getElementById('status-text');
                
                statusText.innerHTML = `<span class="${type}">${message}</span>`;
                status.style.display = 'block';
                
                setTimeout(() => {
                    status.style.display = 'none';
                }, 1500);
            }
            
            showError(message) {
                const loading = document.getElementById('loading');
                loading.innerHTML = `
                    <div style="color: #ff6b6b; text-align: center;">
                        <h3>❌ Lỗi</h3>
                        <p>${message}</p>
                        <button onclick="location.reload()" style="padding: 10px 20px; margin-top: 20px; background: #3498db; color: white; border: none; border-radius: 5px; cursor: pointer;">
                            Thử lại
                        </button>
                    </div>
                `;
            }
            
            hideLoading() {
                document.getElementById('loading').classList.add('hidden');
            }
        }
        
        // Khởi tạo khi DOM ready
        document.addEventListener('DOMContentLoaded', () => {
            new SimpleImageTracker();
        });
    </script>
</body>
</html>
