<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Camera - AR</title>
    <script src="https://aframe.io/releases/1.4.0/aframe.min.js"></script>
    <script src="https://cdn.jsdelivr.net/gh/AR-js-org/AR.js@3.4.5/aframe/build/aframe-ar.js"></script>
    <style>
        body {
            margin: 0;
            font-family: Arial, sans-serif;
        }
        
        #info {
            position: fixed;
            top: 10px;
            left: 10px;
            right: 10px;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 15px;
            border-radius: 10px;
            z-index: 100;
            font-size: 14px;
        }
        
        #status {
            position: fixed;
            bottom: 10px;
            left: 10px;
            right: 10px;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 15px;
            border-radius: 10px;
            z-index: 100;
            font-size: 14px;
        }
        
        .success { color: #4CAF50; }
        .error { color: #f44336; }
        .warning { color: #ff9800; }
    </style>
</head>
<body>
    <div id="info">
        <h3>🎯 Test Camera AR</h3>
        <p>Đây là test đơn giản để kiểm tra camera có hoạt động không.</p>
        <p>Nếu thấy camera, hãy tìm marker Hiro (QR code đen trắng) để test AR.</p>
        <p><a href="https://ar-js-org.github.io/AR.js/data/images/hiro.png" target="_blank" style="color: #4CAF50;">👆 Click để mở Hiro marker</a></p>
    </div>
    
    <div id="status">
        <div id="camera-status">📷 Đang khởi tạo camera...</div>
        <div id="ar-status">🎯 Đang khởi tạo AR...</div>
    </div>

    <a-scene 
        vr-mode-ui="enabled: false"
        renderer="logarithmicDepthBuffer: true"
        embedded
        arjs="trackingMethod: best; sourceType: webcam; debugUIEnabled: true;">
        
        <!-- Marker Hiro mặc định -->
        <a-marker preset="hiro">
            <!-- Nội dung AR đơn giản -->
            <a-box 
                position="0 0.5 0" 
                material="color: red;" 
                animation="property: rotation; to: 0 360 0; loop: true; dur: 2000">
            </a-box>
            
            <a-text 
                value="Hello AR!" 
                position="0 1.5 0" 
                align="center" 
                color="white"
                scale="2 2 2">
            </a-text>
        </a-marker>

        <!-- Camera -->
        <a-entity camera></a-entity>
    </a-scene>

    <script>
        // Kiểm tra camera access
        async function checkCamera() {
            const statusDiv = document.getElementById('camera-status');
            
            try {
                const stream = await navigator.mediaDevices.getUserMedia({ video: true });
                statusDiv.innerHTML = '<span class="success">✅ Camera hoạt động tốt!</span>';
                
                // Dừng stream sau khi test
                stream.getTracks().forEach(track => track.stop());
                
                // Kiểm tra AR
                checkAR();
                
            } catch (error) {
                console.error('Camera error:', error);
                statusDiv.innerHTML = `<span class="error">❌ Lỗi camera: ${error.message}</span>`;
                
                // Hiển thị hướng dẫn sửa lỗi
                setTimeout(() => {
                    statusDiv.innerHTML += `
                        <br><span class="warning">💡 Cách sửa:</span>
                        <br>• Cho phép truy cập camera
                        <br>• Sử dụng HTTPS hoặc localhost
                        <br>• Thử refresh trang
                    `;
                }, 1000);
            }
        }
        
        function checkAR() {
            const arStatusDiv = document.getElementById('ar-status');
            
            // Đợi A-Frame load xong
            document.querySelector('a-scene').addEventListener('loaded', () => {
                arStatusDiv.innerHTML = '<span class="success">✅ AR đã sẵn sàng!</span>';
                
                // Thêm event listener cho marker
                const marker = document.querySelector('a-marker');
                
                marker.addEventListener('markerFound', () => {
                    arStatusDiv.innerHTML = '<span class="success">🎯 Đã tìm thấy Hiro marker!</span>';
                    console.log('Marker found!');
                });
                
                marker.addEventListener('markerLost', () => {
                    arStatusDiv.innerHTML = '<span class="warning">❌ Mất marker</span>';
                    console.log('Marker lost!');
                });
            });
            
            // Timeout fallback
            setTimeout(() => {
                if (arStatusDiv.innerHTML.includes('Đang khởi tạo')) {
                    arStatusDiv.innerHTML = '<span class="error">⚠️ AR khởi tạo chậm, vui lòng đợi...</span>';
                }
            }, 5000);
        }
        
        // Bắt đầu kiểm tra khi trang load xong
        window.addEventListener('load', () => {
            setTimeout(checkCamera, 1000);
        });
        
        // Log errors
        window.addEventListener('error', (e) => {
            console.error('Global error:', e.error);
        });
        
        // Log AR.js events
        window.addEventListener('arjs-video-loaded', () => {
            console.log('AR.js video loaded');
            document.getElementById('camera-status').innerHTML = '<span class="success">✅ AR.js camera loaded!</span>';
        });
        
        window.addEventListener('camera-error', (e) => {
            console.error('Camera error event:', e);
            document.getElementById('camera-status').innerHTML = '<span class="error">❌ Camera error event</span>';
        });
    </script>
</body>
</html>
