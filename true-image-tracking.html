<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no, viewport-fit=cover">
    <title>True Image Tracking - Picsum AR</title>
    
    <!-- MindAR for true image tracking -->
    <script src="https://aframe.io/releases/1.4.0/aframe.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/mind-ar@1.2.5/dist/mindar-image-aframe.prod.js"></script>
    
    <style>
        body {
            margin: 0;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #000;
            overflow: hidden;
        }
        
        #loading {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            z-index: 1000;
        }
        
        #loading.hidden {
            display: none;
        }
        
        .spinner {
            border: 4px solid rgba(255, 255, 255, 0.3);
            border-top: 4px solid #ffffff;
            border-radius: 50%;
            width: 60px;
            height: 60px;
            animation: spin 1s linear infinite;
            margin-bottom: 30px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        #instructions {
            position: fixed;
            top: 20px;
            left: 20px;
            right: 20px;
            background: rgba(0, 0, 0, 0.9);
            color: white;
            padding: 15px;
            border-radius: 12px;
            z-index: 100;
            font-size: 14px;
            backdrop-filter: blur(15px);
        }
        
        #instructions h3 {
            margin: 0 0 10px 0;
            color: #4ECDC4;
        }
        
        #imageList {
            position: fixed;
            bottom: 20px;
            left: 20px;
            right: 20px;
            background: rgba(0, 0, 0, 0.9);
            color: white;
            padding: 15px;
            border-radius: 12px;
            z-index: 100;
            max-height: 200px;
            overflow-y: auto;
            backdrop-filter: blur(15px);
        }
        
        .image-item {
            display: flex;
            align-items: center;
            padding: 10px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-bottom: 8px;
        }
        
        .image-item:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: translateY(-2px);
        }
        
        .image-item img {
            width: 60px;
            height: 45px;
            object-fit: cover;
            border-radius: 6px;
            margin-right: 12px;
        }
        
        .image-info {
            flex: 1;
        }
        
        .image-author {
            font-weight: bold;
            color: #ffffff;
            margin-bottom: 3px;
        }
        
        .image-id {
            font-size: 12px;
            color: #cccccc;
        }
        
        #status {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(0, 0, 0, 0.9);
            color: white;
            padding: 20px;
            border-radius: 15px;
            z-index: 1001;
            text-align: center;
            display: none;
        }
        
        .success { color: #4CAF50; }
        .error { color: #f44336; }
        .warning { color: #ff9800; }
    </style>
</head>
<body>
    <div id="loading">
        <div class="spinner"></div>
        <p>Đang tải hình ảnh từ Picsum API...</p>
        <p>Đang chuẩn bị Image Tracking...</p>
    </div>
    
    <div id="instructions">
        <h3>🎯 True Image Tracking</h3>
        <p>1. Chọn hình ảnh từ danh sách bên dưới</p>
        <p>2. Click để mở full-size và in ra</p>
        <p>3. Hướng camera vào chính hình ảnh đó</p>
        <p>4. Xem AR xuất hiện trên hình ảnh!</p>
    </div>
    
    <div id="imageList">
        <h4>📸 Hình ảnh có thể tracking:</h4>
        <div id="imageContainer">
            Đang tải...
        </div>
    </div>
    
    <div id="status">
        <div id="status-text"></div>
    </div>

    <!-- MindAR Scene -->
    <a-scene 
        mindar-image="imageTargetSrc: ./targets/targets.mind; maxTrack: 3; showStats: false; uiScanning: #scanning; uiError: #error; uiLoading: #loading;"
        color-space="sRGB" 
        renderer="colorManagement: true, physicallyCorrectLights" 
        vr-mode-ui="enabled: false" 
        device-orientation-permission-ui="enabled: false">
        
        <a-assets>
            <!-- Assets sẽ được thêm động -->
        </a-assets>

        <!-- Target entities sẽ được thêm động -->

        <a-entity camera></a-entity>
    </a-scene>

    <script>
        class TrueImageTracker {
            constructor() {
                this.apiUrl = 'https://picsum.photos/v2/list';
                this.images = [];
                this.scene = null;
                this.assets = null;
                this.maxImages = 3; // MindAR free version giới hạn 3 targets
                
                this.init();
            }
            
            async init() {
                try {
                    this.scene = document.querySelector('a-scene');
                    this.assets = document.querySelector('a-assets');
                    
                    // Fetch hình ảnh từ API
                    await this.fetchImages();
                    
                    // Tạo image targets với computer vision
                    await this.createImageTargets();
                    
                    // Ẩn loading screen
                    this.hideLoading();
                    
                    // Setup events
                    this.setupEvents();
                    
                } catch (error) {
                    console.error('Lỗi khởi tạo:', error);
                    this.showError('Có lỗi xảy ra. Đang fallback sang marker tracking...');
                    
                    // Fallback to marker tracking
                    setTimeout(() => {
                        window.location.href = 'index.html';
                    }, 3000);
                }
            }
            
            async fetchImages() {
                const response = await fetch(this.apiUrl);
                const allImages = await response.json();
                
                // Chọn những hình ảnh có nhiều detail để tracking tốt
                this.images = allImages
                    .filter(img => img.width >= 400 && img.height >= 300)
                    .slice(0, this.maxImages);
                
                console.log(`Đã tải ${this.images.length} hình ảnh cho tracking`);
                this.displayImageList();
            }
            
            displayImageList() {
                const container = document.getElementById('imageContainer');
                container.innerHTML = '';
                
                this.images.forEach((image, index) => {
                    const imageItem = document.createElement('div');
                    imageItem.className = 'image-item';
                    
                    const thumbnailUrl = `https://picsum.photos/id/${image.id}/200/150`;
                    
                    imageItem.innerHTML = `
                        <img src="${thumbnailUrl}" alt="Image ${image.id}" loading="lazy">
                        <div class="image-info">
                            <div class="image-author">📷 ${image.author}</div>
                            <div class="image-id">ID: ${image.id} | Target ${index + 1}</div>
                        </div>
                    `;
                    
                    imageItem.addEventListener('click', () => {
                        this.openImageFullSize(image);
                    });
                    
                    container.appendChild(imageItem);
                });
            }
            
            openImageFullSize(image) {
                const fullImageUrl = `https://picsum.photos/id/${image.id}/800/600`;
                window.open(fullImageUrl, '_blank');
            }
            
            async createImageTargets() {
                console.log('Đang tạo image targets...');
                
                // Tạo targets file cho MindAR (simplified)
                await this.generateTargetsFile();
                
                // Tạo AR content cho mỗi target
                this.images.forEach((image, index) => {
                    this.createTargetEntity(image, index);
                });
                
                console.log('Đã tạo xong image targets');
            }
            
            async generateTargetsFile() {
                // Trong thực tế cần compile targets file từ hình ảnh
                // Đây là simplified approach
                console.log('Generating targets file...');
                
                // Tạm thời sử dụng demo targets
                // Trong production cần sử dụng MindAR compiler
            }
            
            createTargetEntity(image, index) {
                const imageId = image.id;
                const imageUrl = `https://picsum.photos/id/${imageId}/400/300`;
                
                // Tạo asset
                const imgAsset = document.createElement('img');
                imgAsset.id = `img-${imageId}`;
                imgAsset.src = imageUrl;
                imgAsset.crossOrigin = 'anonymous';
                this.assets.appendChild(imgAsset);
                
                // Tạo target entity
                const target = document.createElement('a-entity');
                target.setAttribute('mindar-image-target', `targetIndex: ${index}`);
                target.setAttribute('id', `target-${imageId}`);
                
                // AR Content
                const arContent = this.createARContent(image, imageId);
                target.appendChild(arContent);
                
                // Events
                target.addEventListener('targetFound', () => {
                    console.log(`🎯 Tìm thấy hình ảnh: ${image.author}`);
                    this.showStatus(`🎯 Tracking: ${image.author}`, 'success');
                });
                
                target.addEventListener('targetLost', () => {
                    console.log(`❌ Mất tracking: ${image.author}`);
                    this.showStatus('❌ Mất tracking', 'warning');
                });
                
                this.scene.appendChild(target);
            }
            
            createARContent(image, imageId) {
                const group = document.createElement('a-entity');
                
                // Hiển thị hình ảnh với hiệu ứng
                const plane = document.createElement('a-plane');
                plane.setAttribute('position', '0 0 0');
                plane.setAttribute('width', '1');
                plane.setAttribute('height', '0.75');
                plane.setAttribute('material', `src: #img-${imageId}; transparent: true; opacity: 0.9`);
                plane.setAttribute('animation', 'property: rotation; to: 0 0 5; dir: alternate; loop: true; dur: 4000');
                
                // Text thông tin
                const text = document.createElement('a-text');
                text.setAttribute('value', `📷 ${image.author}`);
                text.setAttribute('position', '0 0.6 0');
                text.setAttribute('align', 'center');
                text.setAttribute('color', 'white');
                text.setAttribute('scale', '0.8 0.8 0.8');
                text.setAttribute('geometry', 'primitive: plane; width: auto; height: auto');
                text.setAttribute('material', 'color: rgba(0,0,0,0.7)');
                
                // 3D elements
                const box = document.createElement('a-box');
                box.setAttribute('position', '0.5 0 0.2');
                box.setAttribute('width', '0.2');
                box.setAttribute('height', '0.2');
                box.setAttribute('depth', '0.2');
                box.setAttribute('material', 'color: #4ECDC4');
                box.setAttribute('animation', 'property: rotation; to: 0 360 0; loop: true; dur: 3000');
                
                group.appendChild(plane);
                group.appendChild(text);
                group.appendChild(box);
                
                return group;
            }
            
            showStatus(message, type) {
                const status = document.getElementById('status');
                const statusText = document.getElementById('status-text');
                
                statusText.innerHTML = `<span class="${type}">${message}</span>`;
                status.style.display = 'block';
                
                setTimeout(() => {
                    status.style.display = 'none';
                }, 2000);
            }
            
            showError(message) {
                const loading = document.getElementById('loading');
                loading.innerHTML = `
                    <div style="color: #ff6b6b; text-align: center;">
                        <h3>❌ Lỗi</h3>
                        <p>${message}</p>
                    </div>
                `;
            }
            
            hideLoading() {
                document.getElementById('loading').classList.add('hidden');
            }
            
            setupEvents() {
                // Setup additional events if needed
                console.log('Events setup complete');
            }
        }
        
        // Khởi tạo khi DOM ready
        document.addEventListener('DOMContentLoaded', () => {
            new TrueImageTracker();
        });
    </script>
</body>
</html>
