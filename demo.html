<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Demo - AR Image Tracking</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        h1 {
            text-align: center;
            margin-bottom: 30px;
            font-size: 2.5em;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .demo-section {
            background: rgba(255, 255, 255, 0.1);
            padding: 30px;
            border-radius: 15px;
            margin-bottom: 30px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .demo-images {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        
        .demo-image {
            background: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        }
        
        .demo-image img {
            width: 100%;
            max-width: 250px;
            height: auto;
            border-radius: 8px;
            margin-bottom: 10px;
        }
        
        .demo-image h3 {
            color: #333;
            margin: 10px 0;
        }
        
        .demo-image p {
            color: #666;
            font-size: 14px;
        }
        
        .start-button {
            display: block;
            width: 200px;
            margin: 30px auto;
            padding: 15px 30px;
            background: linear-gradient(135deg, #4ECDC4, #44A08D);
            color: white;
            text-decoration: none;
            border-radius: 50px;
            text-align: center;
            font-size: 18px;
            font-weight: bold;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(78, 205, 196, 0.4);
        }
        
        .start-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(78, 205, 196, 0.6);
        }
        
        .instructions {
            background: rgba(255, 255, 255, 0.15);
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
        }
        
        .instructions ol {
            padding-left: 20px;
        }
        
        .instructions li {
            margin: 10px 0;
            line-height: 1.6;
        }
        
        .features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        
        .feature {
            background: rgba(255, 255, 255, 0.1);
            padding: 20px;
            border-radius: 10px;
            text-align: center;
        }
        
        .feature-icon {
            font-size: 3em;
            margin-bottom: 15px;
        }
        
        .feature h3 {
            margin: 15px 0 10px 0;
        }
        
        .feature p {
            opacity: 0.9;
            line-height: 1.5;
        }
        
        @media (max-width: 768px) {
            body {
                padding: 10px;
            }
            
            h1 {
                font-size: 2em;
            }
            
            .demo-section {
                padding: 20px;
            }
            
            .demo-images {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎯 AR Image Tracking Demo</h1>
        
        <div class="demo-section">
            <h2>🌟 Tính năng chính</h2>
            <div class="features">
                <div class="feature">
                    <div class="feature-icon">📷</div>
                    <h3>Image Tracking</h3>
                    <p>Tracking hình ảnh thực tế và hiển thị nội dung AR 3D</p>
                </div>
                <div class="feature">
                    <div class="feature-icon">🎨</div>
                    <h3>Nội dung 3D</h3>
                    <p>Hiệu ứng 3D đẹp mắt với animation và màu sắc sinh động</p>
                </div>
                <div class="feature">
                    <div class="feature-icon">📱</div>
                    <h3>Responsive</h3>
                    <p>Hoạt động mượt mà trên cả desktop và mobile</p>
                </div>
                <div class="feature">
                    <div class="feature-icon">🔊</div>
                    <h3>Âm thanh</h3>
                    <p>Phản hồi âm thanh khi tracking thành công</p>
                </div>
            </div>
        </div>
        
        <div class="demo-section">
            <h2>📋 Hướng dẫn sử dụng</h2>
            <div class="instructions">
                <ol>
                    <li><strong>Khởi động:</strong> Click nút "Bắt đầu AR" bên dưới</li>
                    <li><strong>Cho phép camera:</strong> Chấp nhận quyền truy cập camera khi được yêu cầu</li>
                    <li><strong>Chọn hình ảnh:</strong> Xem danh sách hình ảnh ở cuối màn hình</li>
                    <li><strong>Mở hình ảnh:</strong> Click vào hình ảnh để mở full-size trong tab mới</li>
                    <li><strong>In hoặc hiển thị:</strong> In hình ảnh ra giấy hoặc hiển thị trên màn hình khác</li>
                    <li><strong>Tracking:</strong> Hướng camera vào hình ảnh để xem AR magic! ✨</li>
                </ol>
            </div>
        </div>
        
        <div class="demo-section">
            <h2>🖼️ Hình ảnh mẫu để test</h2>
            <p>Dưới đây là một số hình ảnh mẫu từ Picsum Photos mà bạn có thể sử dụng để test AR tracking:</p>
            
            <div class="demo-images">
                <div class="demo-image">
                    <img src="https://picsum.photos/id/1/300/200" alt="Sample Image 1">
                    <h3>Laptop & Coffee</h3>
                    <p>ID: 1 - Hình ảnh có nhiều chi tiết, dễ tracking</p>
                </div>
                
                <div class="demo-image">
                    <img src="https://picsum.photos/id/10/300/200" alt="Sample Image 2">
                    <h3>Forest Path</h3>
                    <p>ID: 10 - Hình ảnh tự nhiên với texture phong phú</p>
                </div>
                
                <div class="demo-image">
                    <img src="https://picsum.photos/id/20/300/200" alt="Sample Image 3">
                    <h3>Mountain View</h3>
                    <p>ID: 20 - Cảnh quan đẹp với độ tương phản cao</p>
                </div>
                
                <div class="demo-image">
                    <img src="https://picsum.photos/id/100/300/200" alt="Sample Image 4">
                    <h3>Beach Scene</h3>
                    <p>ID: 100 - Bãi biển với nhiều yếu tố thị giác</p>
                </div>
            </div>
            
            <div class="instructions">
                <p><strong>💡 Mẹo:</strong> Để có kết quả tracking tốt nhất:</p>
                <ul>
                    <li>Sử dụng hình ảnh có độ phân giải cao và rõ nét</li>
                    <li>Đảm bảo ánh sáng đủ khi tracking</li>
                    <li>Giữ camera ổn định, cách hình ảnh 20-50cm</li>
                    <li>Chọn hình ảnh có nhiều chi tiết và texture</li>
                </ul>
            </div>
        </div>
        
        <a href="index.html" class="start-button">
            🚀 Bắt đầu AR Experience
        </a>
        
        <div class="demo-section">
            <h2>🔧 Thông tin kỹ thuật</h2>
            <div class="features">
                <div class="feature">
                    <div class="feature-icon">⚡</div>
                    <h3>A-Frame</h3>
                    <p>Framework WebVR/AR mạnh mẽ</p>
                </div>
                <div class="feature">
                    <div class="feature-icon">🎯</div>
                    <h3>AR.js</h3>
                    <p>Thư viện AR tracking cho web</p>
                </div>
                <div class="feature">
                    <div class="feature-icon">🌐</div>
                    <h3>Picsum API</h3>
                    <p>Nguồn hình ảnh chất lượng cao</p>
                </div>
                <div class="feature">
                    <div class="feature-icon">📱</div>
                    <h3>WebAR</h3>
                    <p>Không cần cài đặt app</p>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
