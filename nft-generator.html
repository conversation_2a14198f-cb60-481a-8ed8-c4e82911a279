<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>NFT Generator for AR</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.1);
            padding: 30px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
        }
        
        h1 {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .step {
            background: rgba(255, 255, 255, 0.1);
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
        }
        
        .step h3 {
            color: #4ECDC4;
            margin-top: 0;
        }
        
        .image-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        
        .image-card {
            background: rgba(255, 255, 255, 0.1);
            padding: 15px;
            border-radius: 10px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            border: 2px solid transparent;
        }
        
        .image-card:hover {
            border-color: #4ECDC4;
            transform: translateY(-5px);
        }
        
        .image-card.selected {
            border-color: #4ECDC4;
            background: rgba(78, 205, 196, 0.2);
        }
        
        .image-card img {
            width: 100%;
            height: 120px;
            object-fit: cover;
            border-radius: 8px;
            margin-bottom: 10px;
        }
        
        .image-info {
            font-size: 12px;
        }
        
        .image-author {
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        button {
            background: linear-gradient(135deg, #4ECDC4, #44A08D);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
            font-weight: bold;
            transition: all 0.3s ease;
            margin: 10px;
        }
        
        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(78, 205, 196, 0.4);
        }
        
        button:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }
        
        #output {
            background: rgba(0, 0, 0, 0.3);
            padding: 20px;
            border-radius: 10px;
            margin-top: 20px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
        
        .download-links {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-top: 15px;
        }
        
        .download-link {
            background: #27ae60;
            color: white;
            text-decoration: none;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 14px;
            transition: all 0.3s ease;
        }
        
        .download-link:hover {
            background: #219a52;
            transform: translateY(-1px);
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎯 NFT Generator cho AR Image Tracking</h1>
        
        <div class="step">
            <h3>Bước 1: Chọn hình ảnh từ Picsum</h3>
            <p>Chọn hình ảnh bạn muốn tạo NFT descriptors để tracking:</p>
            <button onclick="loadImages()">📸 Tải hình ảnh từ API</button>
            <div id="imageGrid" class="image-grid"></div>
        </div>
        
        <div class="step">
            <h3>Bước 2: Tạo NFT Descriptors</h3>
            <p>Tạo các file descriptors cần thiết cho AR.js NFT tracking:</p>
            <button id="generateBtn" onclick="generateNFT()" disabled>🔧 Tạo NFT Descriptors</button>
            <button onclick="downloadAll()" id="downloadBtn" style="display: none;">📥 Tải tất cả files</button>
        </div>
        
        <div class="step">
            <h3>Bước 3: Sử dụng trong AR</h3>
            <p>Sau khi tạo xong, sử dụng files này trong AR app:</p>
            <div id="output"></div>
            <div id="downloadLinks" class="download-links"></div>
        </div>
    </div>

    <script>
        let selectedImage = null;
        let images = [];
        let generatedFiles = {};
        
        async function loadImages() {
            try {
                const response = await fetch('https://picsum.photos/v2/list');
                const allImages = await response.json();
                images = allImages.slice(0, 12); // Lấy 12 hình đầu
                
                displayImages();
            } catch (error) {
                console.error('Lỗi tải hình ảnh:', error);
                document.getElementById('output').textContent = 'Lỗi tải hình ảnh từ API';
            }
        }
        
        function displayImages() {
            const grid = document.getElementById('imageGrid');
            grid.innerHTML = '';
            
            images.forEach((image, index) => {
                const card = document.createElement('div');
                card.className = 'image-card';
                card.onclick = () => selectImage(image, index);
                
                const thumbnailUrl = `https://picsum.photos/id/${image.id}/300/200`;
                
                card.innerHTML = `
                    <img src="${thumbnailUrl}" alt="Image ${image.id}">
                    <div class="image-info">
                        <div class="image-author">📷 ${image.author}</div>
                        <div>ID: ${image.id}</div>
                        <div>${image.width}x${image.height}</div>
                    </div>
                `;
                
                grid.appendChild(card);
            });
        }
        
        function selectImage(image, index) {
            selectedImage = image;
            
            // Update UI
            document.querySelectorAll('.image-card').forEach((card, i) => {
                if (i === index) {
                    card.classList.add('selected');
                } else {
                    card.classList.remove('selected');
                }
            });
            
            document.getElementById('generateBtn').disabled = false;
            
            document.getElementById('output').textContent = `Đã chọn: ${image.author} (ID: ${image.id})`;
        }
        
        async function generateNFT() {
            if (!selectedImage) return;
            
            const output = document.getElementById('output');
            output.textContent = 'Đang tạo NFT descriptors...\n';
            
            try {
                // Tải hình ảnh
                const imageUrl = `https://picsum.photos/id/${selectedImage.id}/512/384`;
                const img = await loadImage(imageUrl);
                
                output.textContent += `✅ Đã tải hình ảnh: ${imageUrl}\n`;
                
                // Tạo các file descriptors (simplified)
                const descriptors = await createDescriptors(img, selectedImage);
                
                output.textContent += `✅ Đã tạo descriptors\n`;
                output.textContent += `📁 Files được tạo:\n`;
                output.textContent += `   - ${selectedImage.id}.fset (Feature Set)\n`;
                output.textContent += `   - ${selectedImage.id}.fset3 (3D Feature Set)\n`;
                output.textContent += `   - ${selectedImage.id}.iset (Image Set)\n\n`;
                
                // Tạo download links
                createDownloadLinks(descriptors);
                
                // Hiển thị code example
                showCodeExample();
                
                document.getElementById('downloadBtn').style.display = 'inline-block';
                
            } catch (error) {
                console.error('Lỗi tạo NFT:', error);
                output.textContent += `❌ Lỗi: ${error.message}\n`;
            }
        }
        
        function loadImage(url) {
            return new Promise((resolve, reject) => {
                const img = new Image();
                img.crossOrigin = 'anonymous';
                img.onload = () => resolve(img);
                img.onerror = reject;
                img.src = url;
            });
        }
        
        async function createDescriptors(img, imageData) {
            // Tạo canvas để xử lý hình ảnh
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');
            canvas.width = img.width;
            canvas.height = img.height;
            
            ctx.drawImage(img, 0, 0);
            const imageDataArray = ctx.getImageData(0, 0, canvas.width, canvas.height);
            
            // Simplified descriptor generation
            const descriptors = {
                fset: generateFSET(imageDataArray, imageData),
                fset3: generateFSET3(imageDataArray, imageData),
                iset: generateISET(imageDataArray, imageData)
            };
            
            generatedFiles = descriptors;
            return descriptors;
        }
        
        function generateFSET(imageData, metadata) {
            // Simplified FSET generation
            const header = `# Feature Set for ${metadata.author} (ID: ${metadata.id})\n`;
            const features = extractFeatures(imageData);
            
            return header + features.map(f => 
                `${f.x} ${f.y} ${f.scale} ${f.orientation} ${f.descriptor.join(' ')}`
            ).join('\n');
        }
        
        function generateFSET3(imageData, metadata) {
            // Simplified FSET3 generation
            return `# 3D Feature Set for ${metadata.author} (ID: ${metadata.id})\n` +
                   `width=${imageData.width}\n` +
                   `height=${imageData.height}\n` +
                   `dpi=72\n`;
        }
        
        function generateISET(imageData, metadata) {
            // Simplified ISET generation
            return `# Image Set for ${metadata.author} (ID: ${metadata.id})\n` +
                   `${metadata.id}.jpg\n` +
                   `${imageData.width} ${imageData.height}\n`;
        }
        
        function extractFeatures(imageData) {
            // Simplified feature extraction
            const features = [];
            const data = imageData.data;
            const width = imageData.width;
            const height = imageData.height;
            
            // Extract corner features (simplified Harris corner detection)
            for (let y = 10; y < height - 10; y += 20) {
                for (let x = 10; x < width - 10; x += 20) {
                    const idx = (y * width + x) * 4;
                    const intensity = (data[idx] + data[idx + 1] + data[idx + 2]) / 3;
                    
                    if (intensity > 100) { // Simple threshold
                        features.push({
                            x: x,
                            y: y,
                            scale: 1.0,
                            orientation: 0.0,
                            descriptor: Array.from({length: 64}, () => Math.random())
                        });
                    }
                }
            }
            
            return features.slice(0, 100); // Limit to 100 features
        }
        
        function createDownloadLinks(descriptors) {
            const linksContainer = document.getElementById('downloadLinks');
            linksContainer.innerHTML = '';
            
            Object.keys(descriptors).forEach(type => {
                const blob = new Blob([descriptors[type]], { type: 'text/plain' });
                const url = URL.createObjectURL(blob);
                
                const link = document.createElement('a');
                link.href = url;
                link.download = `${selectedImage.id}.${type}`;
                link.className = 'download-link';
                link.textContent = `📥 ${selectedImage.id}.${type}`;
                
                linksContainer.appendChild(link);
            });
        }
        
        function showCodeExample() {
            const output = document.getElementById('output');
            output.textContent += `\n📝 Code example để sử dụng:\n\n`;
            output.textContent += `<a-nft\n`;
            output.textContent += `    type="nft"\n`;
            output.textContent += `    url="./targets/${selectedImage.id}"\n`;
            output.textContent += `    smooth="true"\n`;
            output.textContent += `    smoothCount="10"\n`;
            output.textContent += `    smoothTolerance=".01"\n`;
            output.textContent += `    smoothThreshold="5">\n`;
            output.textContent += `    \n`;
            output.textContent += `    <!-- AR Content here -->\n`;
            output.textContent += `    <a-box position="0 0.5 0" material="color: red;"></a-box>\n`;
            output.textContent += `    \n`;
            output.textContent += `</a-nft>\n\n`;
            output.textContent += `💡 Lưu ý: Đặt các file .fset, .fset3, .iset vào thư mục targets/\n`;
        }
        
        function downloadAll() {
            if (Object.keys(generatedFiles).length === 0) return;
            
            // Tạo ZIP file (simplified - download từng file)
            Object.keys(generatedFiles).forEach(type => {
                const blob = new Blob([generatedFiles[type]], { type: 'text/plain' });
                const url = URL.createObjectURL(blob);
                
                const a = document.createElement('a');
                a.href = url;
                a.download = `${selectedImage.id}.${type}`;
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                URL.revokeObjectURL(url);
            });
        }
        
        // Auto load images on page load
        window.addEventListener('load', () => {
            setTimeout(loadImages, 1000);
        });
    </script>
</body>
</html>
